"""YAML utilities for test results"""
import yaml
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

def save_yaml_results(results: Dict[str, Any], file_path: str):
    """Save test results to YAML file"""
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)
    
    # Add timestamp
    results['generated_at'] = datetime.now().isoformat()
    
    with open(file_path, 'w', encoding='utf-8') as yamlfile:
        yaml.dump(results, yamlfile, default_flow_style=False, indent=2)

def load_yaml_results(file_path: str) -> Dict[str, Any]:
    """Load test results from YAML file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as yamlfile:
            return yaml.safe_load(yamlfile)
    except FileNotFoundError:
        return {}
    except Exception as e:
        print(f"Error loading YAML {file_path}: {e}")
        return {}
