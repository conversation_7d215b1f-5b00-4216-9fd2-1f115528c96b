constraint_name: availability_check
successful_matches:
- assigned_clinician: CG003
  clinician_availability: 07:00-19:00
  clinician_name: <PERSON>
  details: 'Score: 100'
  match_quality: Excellent
  patient_id: P001
  scheduled_time: 07:30
  visit_id: V001
- assigned_clinician: CG003
  clinician_availability: 07:00-19:00
  clinician_name: <PERSON>
  details: 'Score: 100'
  match_quality: Excellent
  patient_id: P002
  scheduled_time: '18:30'
  visit_id: V002
- assigned_clinician: CG001
  clinician_availability: 08:00-18:00
  clinician_name: <PERSON>
  details: 'Score: 100'
  match_quality: Excellent
  patient_id: P003
  scheduled_time: '12:00'
  visit_id: V003
- assigned_clinician: CG001
  clinician_availability: 08:00-18:00
  clinician_name: <PERSON>
  details: 'Score: 100'
  match_quality: Excellent
  patient_id: P004
  scheduled_time: 08:00
  visit_id: V004
- assigned_clinician: CG001
  clinician_availability: 08:00-18:00
  clinician_name: <PERSON>
  details: 'Score: 100'
  match_quality: Excellent
  patient_id: P005
  scheduled_time: '16:00'
  visit_id: V005
summary:
  constraint_satisfied: true
  constraint_violations: 0
  success_rate: 100.0%
  successful_matches: 5
  total_visits: 5
test_status: PASS
test_timestamp: '2025-06-01T15:20:56.080396'
violations: []
