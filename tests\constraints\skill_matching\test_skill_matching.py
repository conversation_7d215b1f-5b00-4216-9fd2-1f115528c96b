import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'framework'))

from csv_utils import load_csv_data
from yaml_utils import save_yaml_results
from datetime import datetime

class TestSkillMatching:
    def __init__(self):
        self.constraint_name = "skill_matching"
        self.constraint_dir = os.path.dirname(__file__)
    
    def load_data(self):
        """Load test data from CSV files"""
        self.clinicians = load_csv_data(os.path.join(self.constraint_dir, 'clinicians.csv'))
        self.patients = load_csv_data(os.path.join(self.constraint_dir, 'patients.csv'))
        self.visits = load_csv_data(os.path.join(self.constraint_dir, 'visits.csv'))
        
        print(f"Loaded {len(self.clinicians)} clinicians, {len(self.patients)} patients, {len(self.visits)} visits")
    
    def test_constraint(self):
        """Test skill matching constraint"""
        print(f"\n=== Testing {self.constraint_name} constraint ===")
        
        results = {
            'constraint_name': self.constraint_name,
            'description': 'Ensure clinicians have the required skills for each visit',
            'test_timestamp': datetime.now().isoformat(),
            'visits_evaluated': [],
            'summary': {
                'total_visits': len(self.visits),
                'perfect_matches': 0,
                'partial_matches': 0,
                'no_matches': 0,
                'skill_violations': 0
            }
        }
        
        for visit in self.visits:
            visit_result = self._evaluate_skills(visit)
            results['visits_evaluated'].append(visit_result)
            
            # Update summary based on match quality
            if visit_result['match_quality'] == 'Perfect':
                results['summary']['perfect_matches'] += 1
            elif visit_result['match_quality'] == 'Partial':
                results['summary']['partial_matches'] += 1
            elif visit_result['match_quality'] == 'None':
                results['summary']['no_matches'] += 1
                results['summary']['skill_violations'] += 1
        
        return results
    
    def _evaluate_skills(self, visit):
        """Evaluate skill matching for a single visit"""
        patient = next((p for p in self.patients if p['id'] == visit['patient_id']), None)
        if not patient:
            return {'error': f"Patient {visit['patient_id']} not found"}
        
        required_skills = visit.get('required_skills', '').split(',')
        required_skills = [skill.strip() for skill in required_skills if skill.strip()]
        
        # Evaluate each clinician's skill match
        clinician_matches = []
        
        for clinician in self.clinicians:
            clinician_skills = clinician.get('skills', '').split(',')
            clinician_skills = [skill.strip() for skill in clinician_skills if skill.strip()]
            
            # Calculate skill match
            matched_skills = [skill for skill in required_skills if skill in clinician_skills]
            missing_skills = [skill for skill in required_skills if skill not in clinician_skills]
            
            match_percentage = len(matched_skills) / len(required_skills) * 100 if required_skills else 100
            
            # Determine match quality
            if len(missing_skills) == 0:
                match_quality = "Perfect"
            elif len(matched_skills) > 0:
                match_quality = "Partial"
            else:
                match_quality = "None"
            
            clinician_matches.append({
                'clinician_name': clinician['name'],
                'clinician_id': clinician['id'],
                'matched_skills': matched_skills,
                'missing_skills': missing_skills,
                'match_percentage': round(match_percentage, 1),
                'match_quality': match_quality,
                'hourly_rate': clinician.get('hourly_rate', 'N/A')
            })
        
        # Sort by match quality and percentage
        clinician_matches.sort(key=lambda x: (-x['match_percentage'], x['clinician_name']))
        
        # Determine overall result
        best_match = clinician_matches[0] if clinician_matches else None
        overall_match_quality = best_match['match_quality'] if best_match else 'None'
        
        return {
            'visit_id': visit['id'],
            'patient_name': patient['name'],
            'required_skills': required_skills,
            'clinician_evaluations': clinician_matches,
            'best_match': best_match,
            'match_quality': overall_match_quality,
            'skill_gaps': best_match['missing_skills'] if best_match else required_skills
        }
      def save_results(self, results):
        """Save test results to YAML file"""
        output_file = os.path.join(self.constraint_dir, f'{self.constraint_name}_results.yaml')
        save_yaml_results(results, output_file)
        print(f"Results saved to: {output_file}")

def run_test():
    """Run the skill matching constraint test"""
    test = TestSkillMatching()
    test.load_data()
    results = test.test_constraint()
    test.save_results(results)
    
    # Print summary
    print(f"\n=== Skill Matching Test Summary ===")
    print(f"Total visits: {results['summary']['total_visits']}")
    print(f"Perfect matches: {results['summary']['perfect_matches']}")
    print(f"Partial matches: {results['summary']['partial_matches']}")
    print(f"No matches: {results['summary']['no_matches']}")
    print(f"Skill violations: {results['summary']['skill_violations']}")

if __name__ == "__main__":
    run_test()
