"""CSV data utilities for test framework"""
import csv
from pathlib import Path
from typing import List, Dict, Any

def load_csv_data(file_path: str) -> List[Dict[str, Any]]:
    """Load CSV data and return as list of dictionaries"""
    data = []
    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                data.append(dict(row))
        return data
    except FileNotFoundError:
        print(f"Warning: CSV file not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error loading CSV {file_path}: {e}")
        return []

def save_csv_data(data: List[Dict[str, Any]], file_path: str):
    """Save data to CSV file"""
    if not data:
        return
    
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = data[0].keys()
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
