#!/usr/bin/env python3
"""Test for Travel Time Constraint"""

import csv
import yaml
from pathlib import Path
from datetime import datetime, time

class TestTravelTime:
    """Test travel time constraint for home care scheduling"""
    
    def __init__(self):
        self.test_path = Path(__file__).parent
        self.results = {}
    
    def load_data(self):
        """Load test data from CSV files"""
        
        # Load clinicians
        clinicians = []
        with open(self.test_path / "clinicians.csv") as f:
            reader = csv.DictReader(f)
            for row in reader:
                clinicians.append({
                    'id': row['id'],
                    'name': row['name'],
                    'skills': row['skills'].split(';'),
                    'available_start': row['available_start'],
                    'available_end': row['available_end'],
                    'location': row['location'],
                    'hourly_rate': float(row['hourly_rate'])
                })
                
        # Load patients
        patients = []
        with open(self.test_path / "patients.csv") as f:
            reader = csv.DictReader(f)
            for row in reader:
                patients.append({
                    'id': row['id'],
                    'name': row['name'],
                    'location': row['address']  # Use 'address' field from CSV
                })
                
        # Load visits
        visits = []
        with open(self.test_path / "visits.csv") as f:
            reader = csv.DictReader(f)
            for row in reader:
                visits.append({
                    'id': row['id'],
                    'patient_id': row['patient_id'],
                    'required_skills': row['required_skills'].split(';'),
                    'duration': int(row['duration_minutes']),
                    'scheduled_time': row['scheduled_time'],
                    'location': row['location'],
                    'prev_visit_location': row.get('prev_visit_location', 'Office')
                })
                
        return clinicians, patients, visits
    
    def test_constraint(self):
        """Test the travel time constraint"""
        
        clinicians, patients, visits = self.load_data()
        
        matches = []
        violations = []
        
        for visit in visits:
            result = self._evaluate_visit(visit, clinicians, patients)
            
            if result['can_assign']:
                matches.append({
                    'visit_id': visit['id'],
                    'patient_id': visit['patient_id'],
                    'assigned_clinician': result['best_clinician']['id'],
                    'clinician_name': result['best_clinician']['name'],
                    'scheduled_time': visit['scheduled_time'],
                    'travel_from': visit.get('prev_visit_location', 'Office'),
                    'travel_to': visit['location'],
                    'travel_assessment': result['travel_assessment'],
                    'match_quality': result['match_quality'],
                    'details': result['details']
                })
            else:
                violations.append({
                    'visit_id': visit['id'],
                    'patient_id': visit['patient_id'],
                    'scheduled_time': visit['scheduled_time'],
                    'travel_issue': result['issue'],
                    'travel_from': visit.get('prev_visit_location', 'Office'),
                    'travel_to': visit['location']
                })
        
        # Calculate results
        total_visits = len(visits)
        successful_matches = len(matches)
        constraint_violations = len(violations)
        success_rate = (successful_matches / total_visits * 100) if total_visits > 0 else 0
        
        self.results = {
            'constraint_name': 'travel_time',
            'test_timestamp': datetime.now().isoformat(),
            'summary': {
                'total_visits': total_visits,
                'successful_matches': successful_matches,
                'constraint_violations': constraint_violations,
                'success_rate': f"{success_rate:.1f}%",
                'constraint_satisfied': constraint_violations == 0
            },
            'successful_matches': matches,
            'violations': violations,
            'test_status': 'PASS' if constraint_violations == 0 else 'FAIL'
        }
        
        # Save results
        self.save_results()
        return self.results
    
    def _evaluate_visit(self, visit, clinicians, patients):
        """Evaluate if visit can be assigned based on constraint"""
        
        available_clinicians = []
        best_clinician = None
        best_score = -1
        
        for clinician in clinicians:
            score, can_assign, details, travel_assessment = self._check_clinician_match(visit, clinician, patients)
            
            if can_assign:
                available_clinicians.append(clinician)
                if score > best_score:
                    best_score = score
                    best_clinician = clinician
        
        if best_clinician:
            _, _, _, travel_assessment = self._check_clinician_match(visit, best_clinician, patients)
            return {
                'can_assign': True,
                'best_clinician': best_clinician,
                'travel_assessment': travel_assessment,
                'match_quality': 'Excellent' if best_score > 80 else 'Good' if best_score > 60 else 'Acceptable',
                'available_clinicians': available_clinicians,
                'details': f"Score: {best_score}"
            }
        else:
            return {
                'can_assign': False,
                'issue': 'Excessive travel time between visits',
                'available_clinicians': [],
                'details': 'Travel time constraint violation'
            }
    
    def _check_clinician_match(self, visit, clinician, patients):
        """Check if clinician matches visit requirements - constraint specific logic"""
        
        score = 0
        can_assign = True
        details = []
        travel_assessment = "Unknown"
        
        # Travel Time specific logic
        if 'prev_visit_location' in visit:
            current_location = visit['location']
            prev_location = visit.get('prev_visit_location', 'Office')
            
            # Simplified travel time assessment
            if current_location == prev_location:
                score += 100
                travel_assessment = "No travel required"
                details.append("Same location as previous visit")
            elif prev_location == 'Office':
                score += 95
                travel_assessment = "Start from office"
                details.append("Starting from office location")
            else:
                # Simulate travel time calculation based on location patterns
                travel_time = self._estimate_travel_time(prev_location, current_location)
                
                if travel_time <= 15:  # 15 minutes or less
                    score += 85
                    travel_assessment = f"Short travel ({travel_time} min)"
                    details.append(f"Manageable travel time: {travel_time} minutes")
                elif travel_time <= 30:  # 15-30 minutes
                    score += 70
                    travel_assessment = f"Moderate travel ({travel_time} min)"
                    details.append(f"Moderate travel time: {travel_time} minutes")
                else:  # Over 30 minutes
                    score += 30
                    travel_assessment = f"Long travel ({travel_time} min)"
                    details.append(f"Long travel time: {travel_time} minutes")
                    # Don't reject, but score lower for optimization
        else:
            score += 100
            travel_assessment = "No previous visit constraint"
            details.append("No previous visit to consider")
        
        return score, can_assign, '; '.join(details), travel_assessment
    
    def _estimate_travel_time(self, from_location, to_location):
        """Simple travel time estimation based on address patterns"""
        # Simplified logic - in real implementation would use mapping service
        
        # Extract area from address for basic estimation
        from_area = self._get_area(from_location)
        to_area = self._get_area(to_location)
        
        if from_area == to_area:
            return 10  # Same area
        else:
            return 25  # Different areas
    
    def _get_area(self, address):
        """Extract area from address for travel estimation"""
        if "Oak St" in address or "Pine Ave" in address:
            return "Central"
        elif "Elm Dr" in address or "Maple Rd" in address:
            return "North"
        elif "Cedar Ln" in address:
            return "South"
        else:
            return "Unknown"
    
    def save_results(self):
        """Save test results to YAML"""
        
        output_file = self.test_path / f"{self.results['constraint_name']}_results.yaml"
        
        with open(output_file, 'w') as f:
            yaml.dump(self.results, f, default_flow_style=False, indent=2)
        
        print(f"Results saved to {output_file}")

def main():
    """Run the test"""
    print(f"🏠 Testing Travel Time Constraint for Home Care")
    print("=" * 60)
    
    test = TestTravelTime()
    results = test.test_constraint()
    
    # Display summary
    summary = results['summary']
    print(f"\n📊 Test Results:")
    print(f"   Total Visits: {summary['total_visits']}")
    print(f"   Successful Matches: {summary['successful_matches']}")
    print(f"   Violations: {summary['constraint_violations']}")
    print(f"   Success Rate: {summary['success_rate']}")
    print(f"   Status: {results['test_status']}")
    
    if results['violations']:
        print(f"\n❌ Travel Time Issues:")
        for violation in results['violations']:
            print(f"   • Visit {violation['visit_id']}: {violation['travel_issue']} (from {violation['travel_from']} to {violation['travel_to']})")
    
    if results['successful_matches']:
        print(f"\n✅ Travel Assessments:")
        for match in results['successful_matches']:
            print(f"   • Visit {match['visit_id']}: {match['travel_assessment']} → {match['clinician_name']}")

if __name__ == "__main__":
    main()
