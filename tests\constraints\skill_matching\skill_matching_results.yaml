constraint_name: skill_matching
description: Ensure clinicians have the required skills for each visit
test_timestamp: '2025-06-01T13:43:11.599322'
visits_evaluated:
- visit_id: V001
  patient_name: <PERSON>
  required_skills:
  - Medication
  clinician_evaluations:
  - &id001
    clinician_name: <PERSON>
    clinician_id: CG004
    matched_skills: []
    missing_skills: &id002
    - Medication
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '22.00'
  - clinician_name: <PERSON>
    clinician_id: CG002
    matched_skills: []
    missing_skills:
    - Medication
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '28.00'
  - clinician_name: <PERSON>
    clinician_id: CG003
    matched_skills:
    - Medication
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '32.75'
  - clinician_name: <PERSON>
    clinician_id: CG001
    matched_skills:
    - Medication
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '25.50'
  - clinician_name: <PERSON>
    clinician_id: CG005
    matched_skills: []
    missing_skills:
    - Medication
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '24.25'
  best_match: *id001
  match_quality: None
  skill_gaps: *id002
- visit_id: V002
  patient_name: <PERSON>
  required_skills:
  - Personal Care
  clinician_evaluations:
  - &id003
    clinician_name: David Brown
    clinician_id: CG004
    matched_skills: []
    missing_skills: &id004
    - Personal Care
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '22.00'
  - clinician_name: John Smith
    clinician_id: CG002
    matched_skills:
    - Personal Care
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '28.00'
  - clinician_name: Lisa Chen
    clinician_id: CG003
    matched_skills: []
    missing_skills:
    - Personal Care
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '32.75'
  - clinician_name: Maria Santos
    clinician_id: CG001
    matched_skills:
    - Personal Care
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '25.50'
  - clinician_name: Sarah Johnson
    clinician_id: CG005
    matched_skills:
    - Personal Care
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '24.25'
  best_match: *id003
  match_quality: None
  skill_gaps: *id004
- visit_id: V003
  patient_name: Mary Johnson
  required_skills:
  - Companionship
  clinician_evaluations:
  - &id005
    clinician_name: David Brown
    clinician_id: CG004
    matched_skills:
    - Companionship
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '22.00'
  - clinician_name: John Smith
    clinician_id: CG002
    matched_skills: []
    missing_skills:
    - Companionship
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '28.00'
  - clinician_name: Lisa Chen
    clinician_id: CG003
    matched_skills: []
    missing_skills:
    - Companionship
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '32.75'
  - clinician_name: Maria Santos
    clinician_id: CG001
    matched_skills: []
    missing_skills:
    - Companionship
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '25.50'
  - clinician_name: Sarah Johnson
    clinician_id: CG005
    matched_skills:
    - Companionship
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '24.25'
  best_match: *id005
  match_quality: Perfect
  skill_gaps: []
- visit_id: V004
  patient_name: Frank Miller
  required_skills:
  - Wound Care
  clinician_evaluations:
  - &id007
    clinician_name: David Brown
    clinician_id: CG004
    matched_skills: []
    missing_skills: &id008
    - Wound Care
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '22.00'
  - clinician_name: John Smith
    clinician_id: CG002
    matched_skills: []
    missing_skills:
    - Wound Care
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '28.00'
  - clinician_name: Lisa Chen
    clinician_id: CG003
    matched_skills:
    - Wound Care
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '32.75'
  - clinician_name: Maria Santos
    clinician_id: CG001
    matched_skills: []
    missing_skills:
    - Wound Care
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '25.50'
  - clinician_name: Sarah Johnson
    clinician_id: CG005
    matched_skills: []
    missing_skills:
    - Wound Care
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '24.25'
  best_match: *id007
  match_quality: None
  skill_gaps: *id008
- visit_id: V005
  patient_name: Helen Davis
  required_skills:
  - Physical Therapy
  clinician_evaluations:
  - &id009
    clinician_name: David Brown
    clinician_id: CG004
    matched_skills: []
    missing_skills: &id010
    - Physical Therapy
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '22.00'
  - clinician_name: John Smith
    clinician_id: CG002
    matched_skills:
    - Physical Therapy
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '28.00'
  - clinician_name: Lisa Chen
    clinician_id: CG003
    matched_skills: []
    missing_skills:
    - Physical Therapy
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '32.75'
  - clinician_name: Maria Santos
    clinician_id: CG001
    matched_skills: []
    missing_skills:
    - Physical Therapy
    match_percentage: 0.0
    match_quality: None
    hourly_rate: '25.50'
  - clinician_name: Sarah Johnson
    clinician_id: CG005
    matched_skills:
    - Physical Therapy
    missing_skills: []
    match_percentage: 100.0
    match_quality: Perfect
    hourly_rate: '24.25'
  best_match: *id009
  match_quality: Perfect
  skill_gaps: *id010
summary:
  total_visits: 5
  perfect_matches: 0
  partial_matches: 0
  no_matches: 5
  skill_violations: 5
file_generated: '2025-06-01T13:43:11.599322'
generator: Home Care Scheduling Test Framework
