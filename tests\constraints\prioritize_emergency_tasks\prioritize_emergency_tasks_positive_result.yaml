test_metadata:
  constraint: prioritize_emergency_tasks
  scenario: positive
  test_framework_version: 1.0.0
  timestamp: '2025-06-01T15:16:41.426159'
test_result:
  constraint_satisfied: true
  details:
    assignments_by_priority:
    - assignment_order: 1
      assignment_score: 1000
      clinician_id: C001
      emergency_level: None
      priority: Critical
      task_id: T001
    - assignment_order: 2
      assignment_score: 500
      clinician_id: C001
      emergency_level: None
      priority: High
      task_id: T002
    - assignment_order: 3
      assignment_score: 100
      clinician_id: C002
      emergency_level: None
      priority: Medium
      task_id: T003
    - assignment_order: 4
      assignment_score: 10
      clinician_id: C003
      emergency_level: None
      priority: Low
      task_id: T004
    emergency_first_verified: true
    emergency_tasks: 0
    non_emergency_tasks: 4
    prioritization_correct: true
    priority_violations: []
    total_tasks: 4
  score_impact: 1610
  test_status: PASS
  violation_count: 0
