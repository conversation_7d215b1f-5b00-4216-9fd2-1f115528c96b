# Healthcare Scheduling Test Framework

## Overview

This test framework provides comprehensive testing for healthcare scheduling constraints using a CSV-driven, YAML-output approach. Each constraint has its own test suite with positive, negative, and edge case scenarios.

## Framework Structure

```
tests/
├── framework/                          # Core framework utilities
│   ├── __init__.py                    # Framework package exports
│   ├── csv_parser.py                  # CSV test data parser
│   ├── yaml_output.py                 # YAML result formatter
│   └── test_base.py                   # Base test classes
├── constraints/                       # Individual constraint tests
│   ├── skill_requirement_constraint/  # Hard constraint tests
│   ├── resource_availability_constraint/
│   ├── no_overlapping_tasks/
│   ├── minimize_unassigned_tasks/     # Soft constraint tests
│   ├── prioritize_emergency_tasks/
│   └── infection_control_compliance/  # Healthcare-specific tests
├── integration/                       # Full workflow tests
├── performance/                       # Scalability tests
├── data/                             # Shared test fixtures
└── output/                           # Test results and reports
    └── results/                      # YAML test outputs
```

## Constraint Categories

### Hard Constraints (4 implemented)
- ✅ `skill_requirement_constraint` - Ensures clinicians have required skills
- ✅ `resource_availability_constraint` - Verifies clinician availability
- ✅ `no_overlapping_tasks` - Prevents task overlaps for same clinician
- 🚧 `minimum_rest_between_tasks` - Enforces break periods

### Soft Constraints (11 defined)
- ✅ `minimize_unassigned_tasks` - Maximizes task assignment
- 🚧 `prioritize_emergency_tasks` - Emergency task prioritization
- 🚧 `minimize_travel_time` - Location-based optimization
- 🚧 `balance_workload_distribution` - Even workload distribution
- 🚧 `maximize_continuity_of_care` - Patient-clinician continuity
- 🚧 `preferred_clinician_assignments` - Preference matching
- 🚧 `maximum_consecutive_hours` - Work hour limits
- 🚧 `emergency_task_pinning` - Emergency task handling
- 🚧 `visit_extension_accommodation` - Flexible scheduling
- 🚧 `optimize_emergency_response_time` - Response optimization
- 🚧 `minimize_overtime` - Overtime minimization

### Healthcare-Specific Constraints (3 defined)
- 🚧 `infection_control_compliance` - Infection prevention protocols
- 🚧 `safety_pairing_requirement` - Safety pairing rules
- 🚧 `hipaa_compliance_constraint` - Privacy compliance

## Test Scenarios

Each constraint includes three test scenarios:

### 1. Positive Tests
- ✅ Constraint should be satisfied
- ✅ No violations expected
- ✅ Validates correct behavior

### 2. Negative Tests
- ❌ Constraint should be violated
- ✅ Violations should be detected
- ✅ Validates error handling

### 3. Edge Cases
- 🔍 Boundary conditions
- 🔍 Null/empty values
- 🔍 Complex scenarios

## Input Format (CSV)

### Clinicians
```csv
id,name,skills,shift_start,shift_end,location
C001,Dr. Smith,General;Emergency,08:00,16:00,Hospital_A
C002,Nurse Johnson,ICU;Pediatrics,12:00,20:00,Hospital_A
```

### Tasks
```csv
id,patient_id,required_skills,duration_minutes,priority,location,time_window_start,time_window_end
T001,P001,General,60,Medium,Hospital_A,09:00,11:00
T002,P002,ICU,120,High,Hospital_A,13:00,15:00
```

## Output Format (YAML)

```yaml
test_metadata:
  constraint: skill_requirement_constraint
  scenario: positive
  timestamp: '2025-06-01T10:30:00'
  test_framework_version: '1.0.0'
test_result:
  constraint_satisfied: true
  violation_count: 0
  score_impact: 0
  test_status: PASS
  details:
    total_tasks: 4
    assignable_tasks: 4
    skill_matches:
      - task_id: T001
        required_skill: General
        available_clinicians: [C001, C004]
```

## Running Tests

### Individual Constraint Tests
```bash
# Test specific constraint
pytest tests/constraints/skill_requirement_constraint/

# Test specific scenario
pytest tests/constraints/skill_requirement_constraint/test_skill_requirement_constraint.py::test_positive
```

### Framework Runner
```bash
# Run all constraint tests with comprehensive reporting
python run_test_framework.py
```

### Standard Pytest
```bash
# Run all tests
pytest tests/

# Run with coverage
pytest tests/ --cov=core --cov-report=html
```

## Framework Benefits

### 🚀 Rapid Test Development
- Template-driven test creation
- Minimal boilerplate code
- Consistent test structure

### 📊 Comprehensive Reporting
- YAML-formatted results
- Detailed violation analysis
- Framework-wide test reports

### 🔄 Reusable Components
- Base test classes for each constraint type
- Shared CSV parsing and YAML output
- Common test utilities

### 🧪 Thorough Coverage
- Positive/negative/edge scenarios
- Multiple constraint categories
- Integration and performance tests

### 📈 Scalable Architecture
- Easy to add new constraints
- Supports different test data sets
- Extensible result formats

## Implementation Status

**Completed (✅):**
- Framework utilities (CSV parser, YAML output, base classes)
- 3 constraint test suites (skill requirement, resource availability, no overlapping tasks)
- Test framework runner with comprehensive reporting
- Documentation and README files

**In Progress (🚧):**
- Additional constraint implementations
- Integration test scenarios
- Performance test suite

**Future Enhancements:**
- Automated test data generation
- Visual test result dashboards
- Constraint dependency testing
- Real-time test execution monitoring

## Quick Start

1. **Add a new constraint test:**
   ```bash
   mkdir tests/constraints/your_constraint_name
   # Create CSV files and test file following existing patterns
   ```

2. **Run tests:**
   ```bash
   python run_test_framework.py
   ```

3. **View results:**
   ```bash
   # Check YAML outputs in tests/output/results/
   # Review comprehensive report in tests/output/test_framework_report.yaml
   ```

The framework provides a robust foundation for testing healthcare scheduling constraints with clear, maintainable, and comprehensive test coverage.
