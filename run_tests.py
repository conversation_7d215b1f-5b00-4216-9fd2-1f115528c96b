#!/usr/bin/env python3
"""
Home Care Scheduling Test Framework - Main Test Runner
=====================================================

Primary script for executing all constraint tests in the home care scheduling framework.
Provides comprehensive test execution, reporting, and analysis.

Usage:
    python run_tests.py                    # Run all constraint tests
    python run_tests.py skill_matching     # Run specific constraint
    python run_tests.py --list             # List available constraints
    python run_tests.py --validate         # Validate framework first
    python run_tests.py --setup            # Setup missing framework components
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add framework to path
framework_path = Path(__file__).parent / 'tests' / 'framework'
sys.path.insert(0, str(framework_path))

try:
    from csv_utils import load_csv_data
    from yaml_utils import save_yaml_results
except ImportError as e:
    print(f"❌ Framework utilities not found: {e}")
    print("Run 'python run_tests.py --validate' to check setup")
    sys.exit(1)

class HomeCareTestRunner:
    """Main test runner for home care scheduling constraints"""
    
    def __init__(self):
        self.base_path = Path("tests")
        self.constraints_path = self.base_path / "constraints"
        self.results = {}
    
    def setup_framework(self):
        """Setup missing framework components"""
        print("🔧 Setting up framework components...")
        
        # Create missing directories
        directories = [
            self.base_path / "framework", 
            self.constraints_path
        ]
        
        for directory in directories:
            if not directory.exists():
                directory.mkdir(parents=True, exist_ok=True)
                print(f"✅ Created directory: {directory}")
          # Generate framework utility files if they're missing
        self.generate_framework_utilities()
        
        # Generate additional constraint templates if needed
        self.generate_missing_constraint_templates()
        
        print("✅ Framework setup complete!")
        print("ℹ️  Note: Sample data is generated individually within each constraint directory")
    
    def generate_framework_utilities(self):
        """Generate core framework utility files if they're missing"""
        framework_path = self.base_path / "framework"
        
        # Create __init__.py
        init_file = framework_path / "__init__.py"
        if not init_file.exists():
            print("📝 Creating framework __init__.py...")
            init_content = '"""Home Care Scheduling Test Framework"""\n'
            init_file.write_text(init_content)
            print(f"✅ Created {init_file}")
        
        # Create csv_utils.py
        csv_utils_file = framework_path / "csv_utils.py"
        if not csv_utils_file.exists():
            print("📝 Creating framework csv_utils.py...")
            csv_utils_content = '''"""CSV data utilities for test framework"""
import csv
from pathlib import Path
from typing import List, Dict, Any

def load_csv_data(file_path: str) -> List[Dict[str, Any]]:
    """Load CSV data and return as list of dictionaries"""
    data = []
    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                data.append(dict(row))
        return data
    except FileNotFoundError:
        print(f"Warning: CSV file not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error loading CSV {file_path}: {e}")
        return []

def save_csv_data(data: List[Dict[str, Any]], file_path: str):
    """Save data to CSV file"""
    if not data:
        return
    
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = data[0].keys()
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
'''
            csv_utils_file.write_text(csv_utils_content)
            print(f"✅ Created {csv_utils_file}")
        
        # Create yaml_utils.py
        yaml_utils_file = framework_path / "yaml_utils.py"
        if not yaml_utils_file.exists():
            print("📝 Creating framework yaml_utils.py...")
            yaml_utils_content = '''"""YAML utilities for test results"""
import yaml
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

def save_yaml_results(results: Dict[str, Any], file_path: str):
    """Save test results to YAML file"""
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)
    
    # Add timestamp
    results['generated_at'] = datetime.now().isoformat()
    
    with open(file_path, 'w', encoding='utf-8') as yamlfile:
        yaml.dump(results, yamlfile, default_flow_style=False, indent=2)

def load_yaml_results(file_path: str) -> Dict[str, Any]:
    """Load test results from YAML file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as yamlfile:
            return yaml.safe_load(yamlfile)
    except FileNotFoundError:
        return {}
    except Exception as e:
        print(f"Error loading YAML {file_path}: {e}")
        return {}
'''
            yaml_utils_file.write_text(yaml_utils_content)
            print(f"✅ Created {yaml_utils_file}")
        
        # Create test_base.py
        test_base_file = framework_path / "test_base.py"
        if not test_base_file.exists():
            print("📝 Creating framework test_base.py...")
            test_base_content = '''"""Base test class for constraint testing"""
from typing import Dict, Any, List
from datetime import datetime

class ConstraintTestBase:
    """Base class for all constraint tests"""
    
    def __init__(self, name: str):
        self.name = name
        self.results = {}
        self.start_time = None
        self.end_time = None
    
    def setup(self):
        """Setup test environment"""
        self.start_time = datetime.now()
        print(f"🧪 Starting {self.name} constraint test...")
    
    def teardown(self):
        """Cleanup after test"""
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        print(f"✅ {self.name} completed in {duration:.2f}s")
    
    def validate_data(self, data: List[Dict[str, Any]]) -> bool:
        """Validate input data"""
        if not data:
            print(f"❌ No data provided for {self.name}")
            return False
        return True
    
    def log_result(self, constraint: str, passed: bool, details: str = ""):
        """Log a constraint test result"""
        self.results[constraint] = {
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
'''
            test_base_file.write_text(test_base_content)
            print(f"✅ Created {test_base_file}")
        
        # Note about data per constraint
        print("ℹ️  Sample data is generated individually within each constraint directory")

    def generate_missing_constraint_templates(self):
        """Generate missing constraint test templates if needed"""
        # This method can be expanded to create missing constraint templates
        pass

    def validate_framework(self):
        """Validate framework setup and dependencies"""
        print("🔍 Validating framework setup...")
        validation_passed = True

        # Check if framework directory exists
        framework_path = self.base_path / "framework"
        if not framework_path.exists():
            print(f"❌ Framework directory missing: {framework_path}")
            validation_passed = False

        # Check for required utilities
        required_files = ["csv_utils.py", "yaml_utils.py", "test_base.py"]
        for file_name in required_files:
            file_path = framework_path / file_name
            if not file_path.exists():
                print(f"❌ Missing framework file: {file_path}")
                validation_passed = False

        # Test YAML output (save to temporary location)
        try:
            test_result = {"validation": True, "timestamp": datetime.now().isoformat()}
            temp_dir = Path("temp")
            temp_dir.mkdir(exist_ok=True)
            test_file = temp_dir / "validation_test.yaml"
            save_yaml_results(test_result, str(test_file))
            print(f"✅ YAML output works - saved to {test_file}")
            # Clean up temp file
            test_file.unlink()
            temp_dir.rmdir()
        except Exception as e:
            print(f"❌ YAML output failed: {e}")
            validation_passed = False

        if validation_passed:
            print("\n🎉 Framework validation: PASSED")
        else:
            print("\n❌ Framework validation: FAILED")
            print("Run 'python run_tests.py --setup' to fix missing components")

        return validation_passed

    def discover_constraints(self):
        """Discover all available constraint tests"""
        constraints = []
        if self.constraints_path.exists():
            for item in self.constraints_path.iterdir():
                if item.is_dir():
                    test_file = item / f"test_{item.name}.py"
                    if test_file.exists():
                        constraints.append(item.name)
        return sorted(constraints)

    def run_constraint_test(self, constraint_name):
        """Run a specific constraint test"""
        print(f"\n{'='*60}")
        print(f"Running {constraint_name} constraint test...")
        print(f"{'='*60}")

        try:
            # Import and run the test module
            import importlib.util
            test_file = self.constraints_path / constraint_name / f"test_{constraint_name}.py"

            if not test_file.exists():
                return {
                    'constraint': constraint_name,
                    'status': 'Error',
                    'error': f'Test file not found: {test_file}'
                }

            spec = importlib.util.spec_from_file_location(f"test_{constraint_name}", test_file)
            test_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(test_module)

            # Run the test
            if hasattr(test_module, 'run_test'):
                test_module.run_test()
                return {'constraint': constraint_name, 'status': 'Success', 'error': None}
            elif hasattr(test_module, 'main'):
                test_module.main()
                return {'constraint': constraint_name, 'status': 'Success', 'error': None}
            else:
                return {
                    'constraint': constraint_name,
                    'status': 'Warning',
                    'error': 'No run_test or main function found'
                }

        except Exception as e:
            print(f"Error running {constraint_name} test: {str(e)}")
            return {'constraint': constraint_name, 'status': 'Error', 'error': str(e)}

    def run_all_tests(self, specific_constraints=None):
        """Run all or specific constraint tests"""
        print("Home Care Scheduling Constraint Test Framework")
        print("=" * 60)

        constraints = self.discover_constraints()

        if specific_constraints:
            # Filter to only requested constraints
            constraints = [c for c in constraints if c in specific_constraints]

        if not constraints:
            print("No constraint tests found!")
            return None

        print(f"Found {len(constraints)} constraint tests:")
        for constraint in constraints:
            print(f"  - {constraint}")

        # Run each test
        test_results = []
        for constraint in constraints:
            result = self.run_constraint_test(constraint)
            test_results.append(result)

        # Generate summary
        success_count = sum(1 for r in test_results if r['status'] == 'Success')
        warning_count = sum(1 for r in test_results if r['status'] == 'Warning')
        error_count = sum(1 for r in test_results if r['status'] == 'Error')

        print(f"\n{'='*60}")
        print("TEST EXECUTION SUMMARY")
        print(f"{'='*60}")
        print(f"Total constraints tested: {len(test_results)}")
        print(f"Successful: {success_count}")
        print(f"Warnings: {warning_count}")
        print(f"Errors: {error_count}")

        success_rate = (success_count / len(test_results) * 100) if test_results else 0

        print(f"\nDetailed Results:")
        for result in test_results:
            status_symbol = "✓" if result['status'] == 'Success' else "⚠" if result['status'] == 'Warning' else "✗"
            print(f"  {status_symbol} {result['constraint']}: {result['status']}")
            if result['error']:
                print(f"    Error: {result['error']}")

        return {
            'test_results': test_results,
            'test_run_summary': {
                'total_tests': len(test_results),
                'successful': success_count,
                'warnings': warning_count,
                'errors': error_count,
                'overall_success_rate': f"{success_rate:.1f}%"
            }
        }


def main():
    """Main entry point for the test runner"""
    parser = argparse.ArgumentParser(description="Home Care Scheduling Test Framework")
    parser.add_argument("constraints", nargs="*", help="Specific constraints to test")
    parser.add_argument("--list", action="store_true", help="List available constraints")
    parser.add_argument("--validate", action="store_true", help="Validate framework setup")
    parser.add_argument("--setup", action="store_true", help="Setup missing framework components")

    args = parser.parse_args()

    runner = HomeCareTestRunner()

    if args.list:
        constraints = runner.discover_constraints()
        print("Available constraint tests:")
        for constraint in constraints:
            print(f"  - {constraint}")
        return

    if args.validate:
        validation_passed = runner.validate_framework()
        return

    if args.setup:
        runner.setup_framework()
        print("\n🔧 Framework setup complete. You can now run tests.")
        return

    # Run tests
    results = runner.run_all_tests(args.constraints)

    if results:
        success_rate = float(results['test_run_summary']['overall_success_rate'].rstrip('%'))
        if success_rate == 100:
            print("\n🏆 All tests passed! Framework is fully functional.")
        elif success_rate >= 80:
            print(f"\n✅ Framework is mostly functional ({success_rate}% success rate)")
        else:
            print(f"\n⚠️  Framework needs attention ({success_rate}% success rate)")

    print("\n📋 Next Steps:")
    print("   • Review individual constraint results in tests/constraints/")
    print("   • Run 'python run_tests.py --validate' if issues persist")


if __name__ == "__main__":
    main()
