"""
Healthcare Domain Model - Medical Scheduling Entities
=====================================================

This module contains healthcare-specific implementations of the core scheduling
framework. It extends the abstract base classes with medical constraints,
clinical requirements, and healthcare-specific business logic.

Architecture Diagram:
┌─────────────────────────────────────────────────────────────────────┐
│                    HEALTHCARE DOMAIN MODEL                         │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │    Clinician    │    │ HealthcareTask  │    │     Patient     │  │
│  │  (extends Resource)│  │ (extends Task)  │    │(extends Consumer)│  │
│  │   Supply Side   │    │   Work Items    │    │  Demand Side    │  │
│  │                 │    │                 │    │                 │  │
│  │ + license_number│    │ + patient_id    │    │ + medical_record│  │
│  │ + certifications│    │ + service_type  │    │ + conditions    │  │
│  │ + specializations│   │ + clinical_notes│    │ + medications   │  │
│  │ + supervision   │    │ + equipment_req │    │ + acuity_level  │  │
│  │                 │    │ + infection_ctrl│    │ + care_plan     │  │
│  │ + can_perform() │    │ + get_end_time()│    │ + get_service_  │  │
│  │                 │    │                 │    │   requirements()│  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│           │                       │                       │         │
│           │                       │                       │         │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │HealthcareSkills │    │ ServiceTypes    │    │InfectionControl │  │
│  │ (Value Objects) │    │   (Constants)   │    │     (Enum)      │  │
│  │                 │    │                 │    │                 │  │
│  │ + WOUND_CARE    │    │ + ASSESSMENT    │    │ + STANDARD      │  │
│  │   BASIC→ADVANCED│    │ + WOUND_CARE    │    │ + CONTACT       │  │
│  │ + MEDICATION    │    │ + MEDICATION    │    │ + DROPLET       │  │
│  │   BASIC→IV      │    │ + THERAPY       │    │ + AIRBORNE      │  │
│  │ + THERAPY       │    │ + EMERGENCY     │    │ + ISOLATION     │  │
│  │ + ASSESSMENT    │    │                 │    │                 │  │
│  │ + EMERGENCY     │    │                 │    │                 │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │ Planning Vars   │    │ Real-Time Feat  │    │ Business Logic  │  │
│  │  (TimeFold)     │    │   (Dynamic)     │    │   (Medical)     │  │
│  │                 │    │                 │    │                 │  │
│  │ + assigned_res  │    │ + visit_pinning │    │ + continuity    │  │
│  │ + start_time    │    │ + extensions    │    │ + safety_pairs  │  │
│  │                 │    │ + no_shows      │    │ + supervision   │  │
│  │                 │    │ + emergencies   │    │ + compliance    │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│                                                                     │
│  Healthcare Flow: Patient needs → Care tasks → Clinician assignment │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘

Inheritance Pattern:
    Resource (Abstract Core)
    └── Clinician (Medical professionals)
        ├── Nurse (RN, LPN with medical skills)
        ├── Therapist (PT, OT with therapy skills)
        └── Physician (MD with advanced medical skills)

    Task (Abstract Core)
    └── HealthcareTask (Patient visit specialization)
        ├── Assessment (Health evaluation)
        ├── Treatment (Wound care, medication)
        ├── Therapy (Physical, occupational)
        └── Emergency (Urgent medical response)

    Consumer (Abstract Core)
    └── Patient (Individuals receiving medical care)
        ├── Acute Care (Short-term medical needs)
        ├── Chronic Care (Long-term management)
        ├── Rehabilitation (Recovery-focused)
        └── Palliative Care (Comfort-focused)

Organization:
1. Clinician Implementation (extends core Resource)
2. HealthcareTask Implementation (extends core Task)
3. Patient Implementation (extends core Consumer)
4. Healthcare-Specific Enums (InfectionControlLevel, etc.)
5. Healthcare Value Objects (HealthcareSkills, ServiceTypes)
6. Medical Business Logic and Validation
"""

import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field
from enum import Enum

from core.domain import Resource, Task, Consumer, Location, Skill, SkillLevel, Priority, LocationType


# ============================================================================
# 1. CLINICIAN IMPLEMENTATION (extends core Resource)
# ============================================================================

@dataclass
class Clinician(Resource):
    """
    Healthcare clinician representing nurses, therapists, physicians, and other medical professionals
    Extends core Resource with medical licensing and certification requirements

    Medical Specializations:
    - Registered Nurse (RN): Advanced medical skills, can supervise
    - Licensed Practical Nurse (LPN): Basic medical skills, requires supervision
    - Physical Therapist (PT): Specialized therapy skills
    - Occupational Therapist (OT): Specialized therapy skills
    - Physician (MD): Advanced medical and emergency skills

    Healthcare-Specific Attributes:
        license_number: Professional license identifier
        certifications: List of medical certifications (IV, CPR, etc.)
        specializations: Areas of medical expertise
        requires_supervision: Whether this resource needs supervision
        can_supervise: Whether this resource can supervise others

    Real-Time Healthcare Features:
        Staff illness tracking and replacement
        Emergency availability status
        Regulatory compliance monitoring
    """
    license_number: Optional[str] = None
    certifications: List[str] = field(default_factory=list)
    specializations: List[str] = field(default_factory=list)
    requires_supervision: bool = False
    can_supervise: bool = False

    def can_perform_skill(self, required_skill: Skill) -> bool:
        """
        Check skill compatibility with healthcare-specific hierarchy support
        Implements skill substitution rules for medical competencies
        """
        # Direct skill match
        for our_skill in self.skills:
            if our_skill.id == required_skill.id:
                return True

            # Check if our skill can substitute for required skill
            if our_skill.can_substitute_for(required_skill):
                return True

        return False

    def get_supervision_requirements(self) -> List[str]:
        """Get list of supervision requirements for this resource"""
        requirements = []

        if self.requires_supervision:
            requirements.append("Requires licensed supervisor present")

        # Check for skills requiring supervision
        for skill in self.skills:
            if skill.level == SkillLevel.BASIC and "MEDICATION" in skill.id:
                requirements.append(f"Medication administration requires supervision")

        return requirements

    def can_work_in_location_type(self, location_type: LocationType) -> bool:
        """Check if resource can work in specific location type"""
        # Home healthcare specific logic
        if location_type == LocationType.CLIENT_SITE:  # Patient homes
            # All healthcare resources can work in patient homes
            return True
        elif location_type == LocationType.OFFICE:  # Clinic work
            # Clinic work may require additional certifications
            return "CLINIC_CERTIFIED" in self.certifications
        elif location_type == LocationType.MOBILE:  # Emergency response
            # Emergency response requires specific skills
            return any(skill.id == "EMERGENCY_RESPONSE" for skill in self.skills)

        return True  # Default allow


# ============================================================================
# 2. HEALTHCARE TASK IMPLEMENTATION (extends core Task)
# ============================================================================

@dataclass
class HealthcareTask(Task):
    """
    Healthcare-specific task representing patient visits, procedures, assessments
    Extends core Task with clinical requirements and medical constraints

    Clinical Task Types:
    - Assessment: Health evaluation and monitoring
    - Wound Care: Basic to advanced wound management
    - Medication Administration: Oral, IV, injection medications
    - Physical Therapy: Mobility and strength rehabilitation
    - Occupational Therapy: Daily living skills rehabilitation
    - Emergency Response: Urgent medical situations

    Healthcare-Specific Attributes:
        patient_id: Unique patient identifier
        service_type: Type of medical service being provided
        clinical_notes: Medical notes and special instructions
        equipment_required: Medical equipment needed for the visit
        infection_control_level: Required infection control precautions
        continuity_requirements: Preferences for same clinician

    Planning Variables (TimeFold optimizes these):
        assigned_resource: Which healthcare resource will perform this task
        start_time: When the medical visit will begin

    Real-Time Healthcare Features:
        Visit extensions for complex medical needs
        Emergency priority handling
        No-show and cancellation management
        Infection control compliance
    """
    patient_id: str = ""
    service_type: str = ""  # Will use HealthcareServiceTypes constants
    clinical_notes: str = ""
    equipment_required: List[str] = field(default_factory=list)
    infection_control_level: Optional['InfectionControlLevel'] = None  # Set after enum definition
    continuity_requirements: List[str] = field(default_factory=list)

    # Planning Variables inherited from base Task class:
    # - assigned_resource: Optional[Resource] (will be Clinician instances)
    # - start_time: Optional[datetime.datetime]

    def get_end_time(self) -> Optional[datetime.datetime]:
        """Calculate end time based on start time and total duration (including extensions)"""
        if self.start_time:
            total_duration = self.get_total_duration_minutes()
            return self.start_time + datetime.timedelta(minutes=total_duration)
        return None

    def requires_infection_control(self) -> bool:
        """Check if task requires special infection control measures"""
        return (self.infection_control_level is not None and
                self.infection_control_level != InfectionControlLevel.STANDARD)

    def get_required_equipment(self) -> List[str]:
        """Get list of required equipment based on service type and clinical notes"""
        equipment = self.equipment_required.copy()

        # Add service-type specific equipment
        if self.service_type == HealthcareServiceTypes.WOUND_CARE:
            equipment.extend(["wound_care_kit", "sterile_supplies"])
        elif self.service_type == HealthcareServiceTypes.MEDICATION_ADMIN:
            equipment.extend(["medication_supplies"])
        elif self.service_type == HealthcareServiceTypes.PHYSICAL_THERAPY:
            equipment.extend(["therapy_equipment"])

        # Add infection control equipment
        if self.requires_infection_control():
            equipment.extend(["ppe_kit", "disinfection_supplies"])

        return list(set(equipment))  # Remove duplicates

    def get_clinical_complexity_score(self) -> int:
        """Calculate complexity score based on clinical requirements (1-10)"""
        score = 1

        # Base complexity by service type
        complexity_map = {
            HealthcareServiceTypes.WELLNESS_CHECK: 1,
            HealthcareServiceTypes.ASSESSMENT: 3,
            HealthcareServiceTypes.MEDICATION_ADMIN: 4,
            HealthcareServiceTypes.WOUND_CARE: 5,
            HealthcareServiceTypes.PHYSICAL_THERAPY: 6,
            HealthcareServiceTypes.EMERGENCY_RESPONSE: 10
        }

        score = complexity_map.get(self.service_type, 3)

        # Adjust for infection control requirements
        if (self.infection_control_level and
            self.infection_control_level == InfectionControlLevel.ISOLATION):
            score += 3
        elif self.requires_infection_control():
            score += 1

        # Adjust for equipment requirements
        if len(self.equipment_required) > 3:
            score += 1

        # Adjust for clinical notes complexity (simple heuristic)
        if self.clinical_notes and len(self.clinical_notes) > 100:
            score += 1

        return min(10, score)  # Cap at 10

    def requires_continuity_of_care(self) -> bool:
        """Check if task requires continuity of care (same clinician)"""
        return bool(self.continuity_requirements) or self.service_type in [
            HealthcareServiceTypes.WOUND_CARE,
            HealthcareServiceTypes.PHYSICAL_THERAPY,
            HealthcareServiceTypes.OCCUPATIONAL_THERAPY
        ]

    def is_emergency(self) -> bool:
        """Check if this is an emergency task requiring immediate attention"""
        return (self.priority == Priority.CRITICAL or
                self.service_type == HealthcareServiceTypes.EMERGENCY_RESPONSE)

    def can_be_performed_by(self, resource: 'Clinician') -> bool:
        """Check if this task can be performed by the given resource"""
        # Check basic skill requirements
        for required_skill in self.required_skills:
            if not resource.can_perform_skill(required_skill):
                return False

        # Check location type compatibility
        if self.location and not resource.can_work_in_location_type(self.location.location_type):
            return False

        # Check supervision requirements
        if resource.requires_supervision and not self._has_supervisor_available():
            return False

        # Check infection control capabilities
        if (self.infection_control_level == InfectionControlLevel.ISOLATION and
            "ISOLATION_CERTIFIED" not in resource.certifications):
            return False

        return True

    def _has_supervisor_available(self) -> bool:
        """Check if supervisor is available (simplified implementation)"""
        # In real implementation, this would check for available supervisors
        # in the same time window and location
        return True  # Simplified for now


# ============================================================================
# 3. PATIENT IMPLEMENTATION (extends core Consumer)
# ============================================================================

@dataclass
class Patient(Consumer):
    """
    Healthcare patient representing individuals receiving medical care
    Extends core Consumer with medical conditions, care plans, and health requirements

    Patient Types:
    - Acute Care: Short-term medical needs (post-surgery, injury recovery)
    - Chronic Care: Long-term ongoing medical management (diabetes, COPD)
    - Palliative Care: Comfort-focused care for serious illness
    - Rehabilitation: Recovery-focused therapy and skill rebuilding
    - Preventive Care: Wellness visits and health maintenance

    Healthcare-Specific Attributes:
        medical_record_number: Unique medical identifier
        medical_conditions: List of diagnosed conditions
        care_plan: Current care plan and treatment goals
        medications: Current medication list
        allergies: Known allergies and adverse reactions
        emergency_contacts: Family/caregiver contact information
        insurance_info: Insurance coverage details
        physician_orders: Current medical orders and restrictions

    Patient Availability:
        Family schedules and caregiver availability
        Medical appointments and treatments
        Personal preferences and routines
        Physical limitations and mobility
    """
    medical_record_number: Optional[str] = None
    medical_conditions: List[str] = field(default_factory=list)
    care_plan: Dict[str, Any] = field(default_factory=dict)
    medications: List[Dict[str, str]] = field(default_factory=list)
    allergies: List[str] = field(default_factory=list)
    emergency_contacts: List[Dict[str, str]] = field(default_factory=list)
    insurance_info: Dict[str, str] = field(default_factory=dict)
    physician_orders: List[str] = field(default_factory=list)

    # Healthcare-specific status
    acuity_level: int = 3  # 1-5 scale (1=stable, 5=critical)
    mobility_level: str = "INDEPENDENT"  # INDEPENDENT, ASSISTED, DEPENDENT
    cognitive_status: str = "ALERT"  # ALERT, CONFUSED, IMPAIRED
    fall_risk: bool = False
    infection_precautions: bool = False

    def get_service_requirements(self) -> List[str]:
        """Get list of medical service requirements for this patient"""
        requirements = []

        # Add condition-based requirements
        for condition in self.medical_conditions:
            if "diabetes" in condition.lower():
                requirements.extend(["blood_glucose_monitoring", "medication_management"])
            elif "wound" in condition.lower():
                requirements.extend(["wound_assessment", "dressing_changes"])
            elif "copd" in condition.lower():
                requirements.extend(["respiratory_assessment", "oxygen_monitoring"])

        # Add medication-based requirements
        for medication in self.medications:
            if medication.get("route") == "IV":
                requirements.append("iv_medication_administration")
            elif medication.get("type") == "injection":
                requirements.append("injection_administration")

        # Add physician order requirements
        requirements.extend(self.physician_orders)

        return list(set(requirements))  # Remove duplicates

    def can_receive_service_at(self, time: datetime.datetime) -> bool:
        """Check if patient can receive service at given time"""
        # Check basic availability
        if not self.is_active():
            return False

        # Check preferred time windows
        preferred_windows = self.get_preferred_time_windows()
        if preferred_windows:
            for window in preferred_windows:
                if window.start <= time <= window.end:
                    return True
            return False  # Outside preferred windows

        # Check for time-based restrictions
        hour = time.hour

        # Early morning restrictions for elderly patients
        if self.acuity_level <= 2 and hour < 8:
            return False

        # Late evening restrictions for patients with cognitive issues
        if self.cognitive_status != "ALERT" and hour > 18:
            return False

        # Meal time restrictions (avoid during typical meal times)
        meal_times = [7, 8, 12, 13, 17, 18]  # Breakfast, lunch, dinner hours
        if hour in meal_times and "meal_assistance" not in self.get_service_requirements():
            return False

        return True

    def get_care_complexity_score(self) -> int:
        """Calculate patient care complexity score (1-10)"""
        score = 1

        # Base score from acuity level
        score += self.acuity_level

        # Add for multiple medical conditions
        score += min(len(self.medical_conditions), 3)

        # Add for medication complexity
        iv_meds = len([m for m in self.medications if m.get("route") == "IV"])
        score += iv_meds

        # Add for mobility limitations
        if self.mobility_level == "DEPENDENT":
            score += 2
        elif self.mobility_level == "ASSISTED":
            score += 1

        # Add for cognitive impairment
        if self.cognitive_status == "IMPAIRED":
            score += 2
        elif self.cognitive_status == "CONFUSED":
            score += 1

        # Add for safety risks
        if self.fall_risk:
            score += 1
        if self.infection_precautions:
            score += 1

        return min(10, score)  # Cap at 10

    def requires_specialized_care(self) -> bool:
        """Check if patient requires specialized medical care"""
        specialized_conditions = [
            "wound", "diabetes", "copd", "heart_failure",
            "dialysis", "tracheostomy", "ventilator"
        ]

        return any(
            any(condition in medical_condition.lower()
                for condition in specialized_conditions)
            for medical_condition in self.medical_conditions
        )

    def get_safety_requirements(self) -> List[str]:
        """Get list of safety requirements for this patient"""
        safety_reqs = []

        if self.fall_risk:
            safety_reqs.append("fall_prevention_protocol")

        if self.infection_precautions:
            safety_reqs.append("infection_control_precautions")

        if self.cognitive_status == "IMPAIRED":
            safety_reqs.append("cognitive_safety_measures")

        if self.acuity_level >= 4:
            safety_reqs.append("emergency_response_ready")

        # Check for allergies requiring special precautions
        if self.allergies:
            safety_reqs.append("allergy_precautions")

        return safety_reqs

    def add_medical_condition(self, condition: str, onset_date: Optional[datetime.datetime] = None):
        """Add a new medical condition to patient record"""
        if condition not in self.medical_conditions:
            self.medical_conditions.append(condition)

            # Update service record
            record_text = f"New medical condition added: {condition}"
            if onset_date:
                record_text += f" (onset: {onset_date.date()})"
            self.add_service_record(record_text)

    def add_medication(self, name: str, dosage: str, route: str = "oral", frequency: str = "daily"):
        """Add medication to patient's medication list"""
        medication = {
            "name": name,
            "dosage": dosage,
            "route": route,
            "frequency": frequency,
            "start_date": datetime.datetime.now().isoformat()
        }
        self.medications.append(medication)
        self.add_service_record(f"Medication added: {name} {dosage} {route} {frequency}")

    def update_acuity_level(self, new_level: int, reason: str = ""):
        """Update patient acuity level"""
        old_level = self.acuity_level
        self.acuity_level = new_level

        record_text = f"Acuity level changed from {old_level} to {new_level}"
        if reason:
            record_text += f" - Reason: {reason}"
        self.add_service_record(record_text)


# ============================================================================
# 4. HEALTHCARE-SPECIFIC ENUMS
# ============================================================================

class InfectionControlLevel(Enum):
    """Infection control levels for healthcare tasks"""
    STANDARD = "STANDARD"
    CONTACT_PRECAUTIONS = "CONTACT_PRECAUTIONS"
    DROPLET_PRECAUTIONS = "DROPLET_PRECAUTIONS"
    AIRBORNE_PRECAUTIONS = "AIRBORNE_PRECAUTIONS"
    ISOLATION = "ISOLATION"


# ============================================================================
# 4. HEALTHCARE VALUE OBJECTS (Skills and Service Types)
# ============================================================================

class HealthcareServiceTypes:
    """Common healthcare service types for home healthcare"""
    ASSESSMENT = "ASSESSMENT"
    WOUND_CARE = "WOUND_CARE"
    MEDICATION_ADMIN = "MEDICATION_ADMIN"
    PHYSICAL_THERAPY = "PHYSICAL_THERAPY"
    OCCUPATIONAL_THERAPY = "OCCUPATIONAL_THERAPY"
    WELLNESS_CHECK = "WELLNESS_CHECK"
    EMERGENCY_RESPONSE = "EMERGENCY_RESPONSE"
    SKILLED_NURSING = "SKILLED_NURSING"
    PERSONAL_CARE = "PERSONAL_CARE"


class HealthcareSkills:
    """Predefined healthcare skills with hierarchy for home healthcare"""

    # Nursing Skills
    WOUND_CARE_BASIC = Skill(
        id="WOUND_CARE_BASIC",
        name="Basic Wound Care",
        level=SkillLevel.BASIC
    )

    WOUND_CARE_ADVANCED = Skill(
        id="WOUND_CARE_ADVANCED",
        name="Advanced Wound Care",
        level=SkillLevel.ADVANCED,
        parent_skills=["WOUND_CARE_BASIC"]
    )

    WOUND_CARE_SPECIALIST = Skill(
        id="WOUND_CARE_SPECIALIST",
        name="Wound Care Specialist",
        level=SkillLevel.EXPERT,
        parent_skills=["WOUND_CARE_BASIC", "WOUND_CARE_ADVANCED"]
    )

    # Medication Administration
    MEDICATION_BASIC = Skill(
        id="MEDICATION_BASIC",
        name="Basic Medication Administration",
        level=SkillLevel.BASIC
    )

    MEDICATION_IV = Skill(
        id="MEDICATION_IV",
        name="IV Medication Administration",
        level=SkillLevel.ADVANCED,
        parent_skills=["MEDICATION_BASIC"]
    )

    # Therapy Skills
    PHYSICAL_THERAPY = Skill(
        id="PHYSICAL_THERAPY",
        name="Physical Therapy",
        level=SkillLevel.EXPERT
    )

    OCCUPATIONAL_THERAPY = Skill(
        id="OCCUPATIONAL_THERAPY",
        name="Occupational Therapy",
        level=SkillLevel.EXPERT
    )

    # Assessment Skills
    HEALTH_ASSESSMENT = Skill(
        id="HEALTH_ASSESSMENT",
        name="Health Assessment",
        level=SkillLevel.INTERMEDIATE
    )

    # Emergency Response
    EMERGENCY_RESPONSE = Skill(
        id="EMERGENCY_RESPONSE",
        name="Emergency Response",
        level=SkillLevel.ADVANCED
    )


