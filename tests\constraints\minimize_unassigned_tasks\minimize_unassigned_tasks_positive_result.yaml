test_metadata:
  constraint: minimize_unassigned_tasks
  scenario: positive
  test_framework_version: 1.0.0
  timestamp: '2025-06-01T15:20:56.485286'
test_result:
  constraint_satisfied: true
  details:
    assigned_tasks: 3
    assignment_rate: 100.0%
    penalty_breakdown:
      base_penalty: 0
      high_priority_penalty: 0
      total_penalty: 0
    successful_assignments:
    - assignment_quality: optimal
      clinician_id: C001
      task_id: T001
    - assignment_quality: optimal
      clinician_id: C002
      task_id: T002
    - assignment_quality: optimal
      clinician_id: C003
      task_id: T003
    total_tasks: 3
    unassigned_task_details: []
    unassigned_tasks: 0
  score_impact: 0
  test_status: PASS
  violation_count: 0
