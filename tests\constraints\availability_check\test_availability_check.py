#!/usr/bin/env python3
"""Test for Availability Check Constraint"""

import csv
import yaml
from pathlib import Path
from datetime import datetime, time

class TestAvailabilityCheck:
    """Test availability check constraint for home care scheduling"""
    
    def __init__(self):
        self.test_path = Path(__file__).parent
        self.results = {}
    
    def load_data(self):
        """Load test data from CSV files"""
        
        # Load caregivers
        caregivers = []
        with open(self.test_path / "caregivers.csv") as f:
            reader = csv.DictReader(f)
            for row in reader:
                caregivers.append({
                    'id': row['id'],
                    'name': row['name'],
                    'skills': row['skills'].split(';'),
                    'available_start': row['available_start'],
                    'available_end': row['available_end'],
                    'location': row['location'],
                    'hourly_rate': float(row['hourly_rate'])
                })
        
        # Load patients
        patients = []
        with open(self.test_path / "patients.csv") as f:
            reader = csv.DictReader(f)
            for row in reader:
                patients.append(row)
        
        # Load visits
        visits = []
        with open(self.test_path / "visits.csv") as f:
            reader = csv.DictReader(f)
            for row in reader:
                visits.append(row)
        
        return caregivers, patients, visits
    
    def test_constraint(self):
        """Test the availability check constraint"""
        
        caregivers, patients, visits = self.load_data()
        
        matches = []
        violations = []
        
        for visit in visits:
            result = self._evaluate_visit(visit, caregivers, patients)
            
            if result['can_assign']:
                matches.append({
                    'visit_id': visit['id'],
                    'patient_id': visit['patient_id'],
                    'assigned_caregiver': result['best_caregiver']['id'],
                    'caregiver_name': result['best_caregiver']['name'],
                    'scheduled_time': visit['scheduled_time'],
                    'caregiver_availability': f"{result['best_caregiver']['available_start']}-{result['best_caregiver']['available_end']}",
                    'match_quality': result['match_quality'],
                    'details': result['details']
                })
            else:
                violations.append({
                    'visit_id': visit['id'],
                    'patient_id': visit['patient_id'],
                    'scheduled_time': visit['scheduled_time'],
                    'issue': result['issue'],
                    'available_caregivers': [f"{c['id']} ({c['available_start']}-{c['available_end']})" for c in result['available_caregivers']]
                })
        
        # Calculate results
        total_visits = len(visits)
        successful_matches = len(matches)
        constraint_violations = len(violations)
        success_rate = (successful_matches / total_visits * 100) if total_visits > 0 else 0
        
        self.results = {
            'constraint_name': 'availability_check',
            'test_timestamp': datetime.now().isoformat(),
            'summary': {
                'total_visits': total_visits,
                'successful_matches': successful_matches,
                'constraint_violations': constraint_violations,
                'success_rate': f"{success_rate:.1f}%",
                'constraint_satisfied': constraint_violations == 0
            },
            'successful_matches': matches,
            'violations': violations,
            'test_status': 'PASS' if constraint_violations == 0 else 'FAIL'
        }
        
        # Save results
        self.save_results()
        return self.results
    
    def _evaluate_visit(self, visit, caregivers, patients):
        """Evaluate if visit can be assigned based on constraint"""
        
        available_caregivers = []
        best_caregiver = None
        best_score = -1
        
        for caregiver in caregivers:
            score, can_assign, details = self._check_caregiver_match(visit, caregiver, patients)
            
            if can_assign:
                available_caregivers.append(caregiver)
                if score > best_score:
                    best_score = score
                    best_caregiver = caregiver
        
        if best_caregiver:
            return {
                'can_assign': True,
                'best_caregiver': best_caregiver,
                'match_quality': 'Excellent' if best_score > 80 else 'Good' if best_score > 60 else 'Acceptable',
                'available_caregivers': available_caregivers,
                'details': f"Score: {best_score}"
            }
        else:
            return {
                'can_assign': False,
                'issue': 'No caregiver available during scheduled time',
                'available_caregivers': [],
                'details': 'Availability constraint violation'
            }
    
    def _check_caregiver_match(self, visit, caregiver, patients):
        """Check if caregiver matches visit requirements - constraint specific logic"""
        
        score = 0
        can_assign = True
        details = []
        
        # Availability Check specific logic
        # Check if caregiver is available during visit time
        visit_time = datetime.strptime(visit['scheduled_time'], "%H:%M").time()
        available_start = datetime.strptime(caregiver['available_start'], "%H:%M").time()
        available_end = datetime.strptime(caregiver['available_end'], "%H:%M").time()
        
        if available_start <= visit_time <= available_end:
            score += 100
            details.append(f"Available during visit time {visit['scheduled_time']}")
        else:
            can_assign = False
            details.append(f"Not available at {visit['scheduled_time']} (works {caregiver['available_start']}-{caregiver['available_end']})")
        
        return score, can_assign, '; '.join(details)
    
    def save_results(self):
        """Save test results to YAML"""
        
        output_file = self.test_path / f"{self.results['constraint_name']}_results.yaml"
        
        with open(output_file, 'w') as f:
            yaml.dump(self.results, f, default_flow_style=False, indent=2)
        
        print(f"Results saved to {output_file}")

def main():
    """Run the test"""
    print(f"🏠 Testing Availability Check Constraint for Home Care")
    print("=" * 60)
    
    test = TestAvailabilityCheck()
    results = test.test_constraint()
    
    # Display summary
    summary = results['summary']
    print(f"\n📊 Test Results:")
    print(f"   Total Visits: {summary['total_visits']}")
    print(f"   Successful Matches: {summary['successful_matches']}")
    print(f"   Violations: {summary['constraint_violations']}")
    print(f"   Success Rate: {summary['success_rate']}")
    print(f"   Status: {results['test_status']}")
    
    if results['violations']:
        print(f"\n❌ Availability Violations:")
        for violation in results['violations']:
            print(f"   • Visit {violation['visit_id']} at {violation['scheduled_time']}: {violation['issue']}")
    
    if results['successful_matches']:
        print(f"\n✅ Successful Matches:")
        for match in results['successful_matches']:
            print(f"   • Visit {match['visit_id']} at {match['scheduled_time']} → {match['caregiver_name']} (available {match['caregiver_availability']})")

if __name__ == "__main__":
    main()
