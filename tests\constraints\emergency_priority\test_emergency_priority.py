import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'framework'))

from csv_utils import load_csv_data
from yaml_utils import save_yaml_results
from datetime import datetime, timedelta

class TestEmergencyPriority:
    def __init__(self):
        self.constraint_name = "emergency_priority"
        self.constraint_dir = os.path.dirname(__file__)
    
    def load_data(self):
        """Load test data from CSV files"""
        self.clinicians = load_csv_data(os.path.join(self.constraint_dir, 'clinicians.csv'))
        self.patients = load_csv_data(os.path.join(self.constraint_dir, 'patients.csv'))
        self.visits = load_csv_data(os.path.join(self.constraint_dir, 'visits.csv'))
        
        print(f"Loaded {len(self.clinicians)} clinicians, {len(self.patients)} patients, {len(self.visits)} visits")
    
    def test_constraint(self):
        """Test emergency priority constraint"""
        print(f"\n=== Testing {self.constraint_name} constraint ===")
        
        results = {
            'constraint_name': self.constraint_name,
            'description': 'Ensure emergency and high-priority visits are assigned to qualified clinicians with appropriate response times',
            'test_timestamp': datetime.now().isoformat(),
            'visits_evaluated': [],
            'summary': {
                'total_visits': len(self.visits),
                'emergency_visits': 0,
                'high_priority_visits': 0,
                'response_time_violations': 0
            }
        }
        
        for visit in self.visits:
            visit_result = self._evaluate_priority(visit)
            results['visits_evaluated'].append(visit_result)
            
            # Update summary statistics
            if visit_result['priority'] == 'Emergency':
                results['summary']['emergency_visits'] += 1
            elif visit_result['priority'] == 'High':
                results['summary']['high_priority_visits'] += 1
            
            if visit_result['response_time_ok'] == False:
                results['summary']['response_time_violations'] += 1
        
        return results
    
    def _evaluate_priority(self, visit):
        """Evaluate a visit based on priority and response time requirements"""
        patient = next(p for p in self.patients if p['id'] == visit['patient_id'])
        
        # Find qualified clinicians
        qualified_clinicians = []
        for clinician in self.clinicians:
            if self._check_qualifications(clinician, visit):
                response_time = self._calculate_response_time(clinician, patient)
                qualified_clinicians.append({
                    'id': clinician['id'],
                    'name': clinician['name'],
                    'response_time': response_time
                })
        
        # Determine visit priority
        priority = 'Routine'
        response_time_ok = True
        if visit['urgency_level'] == 'Emergency':
            priority = 'Emergency'
            # Check response time for emergency visits
            required_response_time = int(visit['response_needed_minutes'])
            if not any(c['response_time'] <= required_response_time for c in qualified_clinicians):
                response_time_ok = False
        elif visit['urgency_level'] == 'Urgent':
            priority = 'High'
        
        return {
            'visit_id': visit['id'],
            'patient_id': visit['patient_id'],
            'priority': priority,
            'response_time_ok': response_time_ok,
            'qualified_clinicians': qualified_clinicians
        }
    
    def _check_qualifications(self, clinician, visit):
        """Check if a clinician is qualified for a visit"""
        # Check required skills
        required_skills = visit.get('required_skills', '').split(',')
        clinician_skills = clinician.get('skills', '').split(',')
        
        has_required_skills = all(skill in clinician_skills for skill in required_skills)
        
        # Check emergency certification if required
        is_emergency_certified = clinician.get('emergency_certified', 'No').lower() == 'yes'
        emergency_care_required = 'Emergency Care' in required_skills and visit['urgency_level'] == 'Emergency'
        
        return has_required_skills and (not emergency_care_required or is_emergency_certified)
    
    def _calculate_response_time(self, clinician, patient):
        """Calculate response time for a clinician to a patient"""
        # Simplified response time calculation based on clinician's response time capability
        return int(clinician.get('response_time_minutes', 60))
    
    def save_results(self, results):
        """Save test results to YAML file"""
        output_file = os.path.join(self.constraint_dir, f'{self.constraint_name}_results.yaml')
        save_yaml_results(results, output_file)
        print(f"Results saved to: {output_file}")

def run_test():
    """Run the emergency priority constraint test"""
    test = TestEmergencyPriority()
    test.load_data()
    results = test.test_constraint()
    test.save_results(results)
    
    # Print summary
    print(f"\n=== Emergency Priority Test Summary ===")
    print(f"Total visits evaluated: {results['summary']['total_visits']}")
    print(f"Emergency visits: {results['summary']['emergency_visits']}")
    print(f"High-priority visits: {results['summary']['high_priority_visits']}")
    print(f"Response time violations: {results['summary']['response_time_violations']}")

if __name__ == "__main__":
    run_test()
