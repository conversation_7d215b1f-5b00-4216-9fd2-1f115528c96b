test_metadata:
  constraint: resource_availability_constraint
  scenario: negative
  test_framework_version: 1.0.0
  timestamp: '2025-06-01T15:20:56.501181'
test_result:
  constraint_satisfied: false
  details:
    constraint_properly_enforced: true
    expected_violations: 3
    total_tasks: 4
    violations_detected:
    - availability_conflicts:
      - clinician_id: C001
        conflict_type: time_outside_shift
        shift_hours: 08:00-16:00
        task_hours: 05:00-06:00
      - clinician_id: C002
        conflict_type: time_outside_shift
        shift_hours: 12:00-20:00
        task_hours: 05:00-06:00
      - clinician_id: C003
        conflict_type: time_outside_shift
        shift_hours: 06:00-14:00
        task_hours: 05:00-06:00
      - clinician_id: C004
        conflict_type: time_outside_shift
        shift_hours: 14:00-22:00
        task_hours: 05:00-06:00
      task_id: T001
    - availability_conflicts:
      - clinician_id: C001
        conflict_type: time_outside_shift
        shift_hours: 08:00-16:00
        task_hours: 16:00-19:00
      - clinician_id: C003
        conflict_type: time_outside_shift
        shift_hours: 06:00-14:00
        task_hours: 16:00-19:00
      task_id: T003
    - availability_conflicts:
      - clinician_id: C001
        conflict_type: time_outside_shift
        shift_hours: 08:00-16:00
        task_hours: 23:00-23:45
      - clinician_id: C002
        conflict_type: time_outside_shift
        shift_hours: 12:00-20:00
        task_hours: 23:00-23:45
      - clinician_id: C003
        conflict_type: time_outside_shift
        shift_hours: 06:00-14:00
        task_hours: 23:00-23:45
      - clinician_id: C004
        conflict_type: time_outside_shift
        shift_hours: 14:00-22:00
        task_hours: 23:00-23:45
      task_id: T004
  score_impact: -150
  test_status: PASS
  violation_count: 3
