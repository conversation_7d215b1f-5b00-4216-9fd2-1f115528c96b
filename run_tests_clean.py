#!/usr/bin/env python3
"""
Home Care Scheduling Test Framework - Main Test Runner
=====================================================

Primary script for executing all constraint tests in the home care scheduling framework.
Provides comprehensive test execution, reporting, and analysis.

Usage:
    python run_tests.py                    # Run all constraint tests
    python run_tests.py skill_matching     # Run specific constraint
    python run_tests.py --list             # List available constraints
    python run_tests.py --validate         # Validate framework first
    python run_tests.py --setup            # Setup missing framework components
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add framework to path
framework_path = Path(__file__).parent / 'tests' / 'framework'
sys.path.insert(0, str(framework_path))

try:
    from csv_utils import load_csv_data
    from yaml_utils import save_yaml_results
except ImportError as e:
    print(f"❌ Framework utilities not found: {e}")
    print("Run 'python run_tests.py --validate' to check setup")
    sys.exit(1)

class HomeCareTestRunner:
    """Main test runner for home care scheduling constraints"""
    
    def __init__(self):
        self.base_path = Path("tests")
        self.constraints_path = self.base_path / "constraints"
        self.results = {}
    
    def setup_framework(self):
        """Setup missing framework components"""
        print("🔧 Setting up framework components...")
        
        # Create missing directories
        directories = [
            self.base_path / "framework", 
            self.constraints_path
        ]
        
        for directory in directories:
            if not directory.exists():
                directory.mkdir(parents=True, exist_ok=True)
                print(f"✅ Created directory: {directory}")
        
        # Generate additional constraint templates if needed
        self.generate_missing_constraint_templates()
        
        print("✅ Framework setup complete!")
        print("ℹ️  Note: Sample data is generated individually within each constraint directory")
    
    def generate_missing_constraint_templates(self):
        """Generate templates for missing constraint tests"""
        needed_constraints = [
            'workload_balance',
            'travel_optimization', 
            'time_preferences'
        ]
        
        for constraint in needed_constraints:
            constraint_dir = self.constraints_path / constraint
            constraint_file = constraint_dir / f"test_{constraint}.py"
            
            if not constraint_file.exists():
                print(f"📝 Creating template for {constraint}...")
                
                constraint_dir.mkdir(exist_ok=True)
                
                template_content = f'''#!/usr/bin/env python3
"""
{constraint.replace('_', ' ').title()} Constraint Test
"""

import sys
from pathlib import Path

# Add framework to path
framework_path = Path(__file__).parent.parent / 'framework'
sys.path.insert(0, str(framework_path))

from test_base import ConstraintTestBase
from csv_utils import load_csv_data
from yaml_utils import save_yaml_results

class {constraint.replace('_', '').title()}Test(ConstraintTestBase):
    """Test for {constraint.replace('_', ' ')} constraint"""
    
    def __init__(self):
        super().__init__("{constraint}")
    
    def run_test(self):
        """Execute {constraint.replace('_', ' ')} constraint test"""
        self.setup()
        
        try:
            # TODO: Load constraint-specific test data
            # Each constraint should generate its own test data
            # Example: clinicians = self.generate_clinicians_data()
            
            # Implement specific constraint logic here
            result = self.test_{constraint}()
            
            self.log_result("{constraint}", result, f"{constraint.replace('_', ' ').title()} constraint validation")
            
        except Exception as e:
            self.log_result("{constraint}", False, f"Error: {{str(e)}}")
        
        finally:
            self.teardown()
        
        return self.results
    
    def test_{constraint}(self):
        """Implement specific {constraint.replace('_', ' ')} logic"""
        print(f"📋 Testing {constraint.replace('_', ' ')}...")
        
        # TODO: Implement constraint-specific validation logic
        # Generate test data specific to this constraint
        # Run constraint validation
        # Return True if constraint is satisfied, False otherwise
        
        return True

if __name__ == "__main__":
    test = {constraint.replace('_', '').title()}Test()
    results = test.run_test()
    
    # Save results in constraint directory
    constraint_dir = Path(__file__).parent
    save_yaml_results(results, str(constraint_dir / "{constraint}_results.yaml"))
    
    # Print summary
    passed = all(r.get('passed', False) for r in results.values())
    status = "✅ PASSED" if passed else "❌ FAILED"
    print(f"\\n{constraint.replace('_', ' ').title()} Test: {{status}}")
'''
                
                constraint_file.write_text(template_content)
                print(f"✅ Created {constraint_file}")
    
    def discover_constraints(self) -> List[str]:
        """Discover all available constraint tests"""
        if not self.constraints_path.exists():
            return []
        
        constraints = []
        for item in self.constraints_path.iterdir():
            if item.is_dir() and (item / f"test_{item.name}.py").exists():
                constraints.append(item.name)
        
        return sorted(constraints)
    
    def run_constraint_test(self, constraint_name: str) -> Dict[str, Any]:
        """Run a specific constraint test"""
        constraint_path = self.constraints_path / constraint_name
        test_file = constraint_path / f"test_{constraint_name}.py"
        
        if not test_file.exists():
            return {
                'status': 'SKIP',
                'message': f'Test file not found: {test_file}',
                'timestamp': datetime.now().isoformat()
            }
        
        print(f"\n🔧 Testing {constraint_name}...")
        
        try:
            # Import and run the test module
            sys.path.insert(0, str(constraint_path))
            
            # Import the test module
            test_module_name = f"test_{constraint_name}"
            spec = __import__(test_module_name)
            
            # Check if module has a main function or run_test function
            if hasattr(spec, 'run_test'):
                results = spec.run_test()
            elif hasattr(spec, 'main'):
                results = spec.main()
            else:
                # Try to find test class and run it
                for attr_name in dir(spec):
                    attr = getattr(spec, attr_name)
                    if (isinstance(attr, type) and 
                        attr_name.endswith('Test') and 
                        hasattr(attr, 'run_test')):
                        test_instance = attr()
                        results = test_instance.run_test()
                        break
                else:
                    return {
                        'status': 'ERROR',
                        'message': 'No runnable test function found',
                        'timestamp': datetime.now().isoformat()
                    }
            
            # Check if results file was created
            results_file = constraint_path / f"{constraint_name}_results.yaml"
            if results_file.exists():
                print(f"   ✅ Results saved to {results_file}")
            
            # Extract key metrics
            if isinstance(results, dict):
                summary = results.get('summary', {})
                total_visits = summary.get('total_visits', 0)
                successful_matches = summary.get('successful_matches', 0)
                violations = summary.get('constraint_violations', 0)
                
                success_rate = (successful_matches / total_visits * 100) if total_visits > 0 else 0
                
                status = 'PASS' if violations == 0 else 'FAIL'
                print(f"   📊 {successful_matches}/{total_visits} visits matched ({success_rate:.1f}%)")
                
                return {
                    'status': status,
                    'total_visits': total_visits,
                    'successful_matches': successful_matches,
                    'violations': violations,
                    'success_rate': f"{success_rate:.1f}%",
                    'timestamp': datetime.now().isoformat(),
                    'results_file': str(results_file)
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'Test completed successfully',
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return {
                'status': 'ERROR',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
        finally:
            # Clean up path
            if str(constraint_path) in sys.path:
                sys.path.remove(str(constraint_path))
    
    def run_all_tests(self, constraints: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run all or specified constraint tests"""
        available_constraints = self.discover_constraints()
        
        if not available_constraints:
            print("❌ No constraint tests found!")
            return {}
        
        # Use specified constraints or all available
        constraints_to_run = constraints or available_constraints
        
        # Validate specified constraints exist
        invalid_constraints = [c for c in constraints_to_run if c not in available_constraints]
        if invalid_constraints:
            print(f"❌ Invalid constraints: {', '.join(invalid_constraints)}")
            print(f"Available constraints: {', '.join(available_constraints)}")
            return {}
        
        print(f"🏠 Home Care Scheduling Test Framework")
        print("=" * 60)
        print(f"📋 Running {len(constraints_to_run)} constraint tests:")
        for constraint in constraints_to_run:
            print(f"   • {constraint}")
        
        # Run tests
        for constraint in constraints_to_run:
            result = self.run_constraint_test(constraint)
            self.results[constraint] = result
        
        # Generate summary report
        return self.generate_summary_report()
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate comprehensive summary report"""
        print(f"\n📊 Generating Test Summary Report...")
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results.values() if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.results.values() if r['status'] == 'FAIL'])
        error_tests = len([r for r in self.results.values() if r['status'] == 'ERROR'])
        
        summary_report = {
            'test_run_summary': {
                'timestamp': datetime.now().isoformat(),
                'framework_version': '2.0.0-consolidated',
                'total_constraints_tested': total_tests,
                'constraints_passed': passed_tests,
                'constraints_failed': failed_tests,
                'constraints_error': error_tests,
                'overall_success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            'individual_results': self.results,
            'recommendations': self._generate_recommendations()
        }
        
        # Save summary report to main directory
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        report_file = reports_dir / f"test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        save_yaml_results(summary_report, str(report_file))
        
        # Display summary
        print(f"\n🎯 Test Summary:")
        print(f"   📊 Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   🚨 Errors: {error_tests}")
        print(f"   📈 Success Rate: {summary_report['test_run_summary']['overall_success_rate']}")
        
        if failed_tests > 0 or error_tests > 0:
            print(f"\n⚠️  Issues Found:")
            for constraint, result in self.results.items():
                if result['status'] in ['FAIL', 'ERROR']:
                    print(f"   • {constraint}: {result['status']} - {result.get('message', 'Check logs')}")
        
        print(f"\n💾 Detailed report saved to: {report_file}")
        
        return summary_report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        failed_tests = [name for name, result in self.results.items() if result['status'] == 'FAIL']
        error_tests = [name for name, result in self.results.items() if result['status'] == 'ERROR']
        
        if failed_tests:
            recommendations.append(f"Review constraint logic for: {', '.join(failed_tests)}")
            recommendations.append("Check if test data reflects realistic scenarios")
        
        if error_tests:
            recommendations.append(f"Fix implementation errors in: {', '.join(error_tests)}")
            recommendations.append("Verify test file structure and imports")
        
        if not failed_tests and not error_tests:
            recommendations.extend([
                "All tests passed! Consider adding more edge cases",
                "Add performance tests for larger datasets",
                "Integrate with production scheduling system"
            ])
        
        return recommendations
    
    def list_constraints(self):
        """List all available constraint tests"""
        constraints = self.discover_constraints()
        
        print(f"🏠 Available Home Care Constraint Tests:")
        print("=" * 45)
        
        if not constraints:
            print("No constraint tests found!")
            return
        
        for i, constraint in enumerate(constraints, 1):
            constraint_path = self.constraints_path / constraint
            
            # Check if results exist
            results_file = constraint_path / f"{constraint}_results.yaml"
            status = "✅ Has Results" if results_file.exists() else "📋 Ready to Run"
            
            print(f"{i:2d}. {constraint:<25} {status}")
        
        print(f"\nTotal: {len(constraints)} constraint tests available")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Home Care Scheduling Test Framework")
    parser.add_argument('constraints', nargs='*', help='Specific constraints to test')
    parser.add_argument('--list', action='store_true', help='List available constraints')
    parser.add_argument('--validate', action='store_true', help='Validate framework before running')
    parser.add_argument('--setup', action='store_true', help='Setup missing framework components')
    
    args = parser.parse_args()
    
    runner = HomeCareTestRunner()
    
    if args.list:
        runner.list_constraints()
        return
    
    if args.validate:
        print("🔍 Validating Test Framework Setup...")
        validation_passed = True
        
        # Check framework utilities
        try:
            from csv_utils import load_csv_data
            from yaml_utils import save_yaml_results
            print("✅ Framework utilities available")
        except ImportError as e:
            print(f"❌ Framework utilities import failed: {e}")
            validation_passed = False
        
        # Check directory structure
        required_dirs = [
            runner.base_path,
            runner.constraints_path,
            runner.base_path / "framework"
        ]
        
        for directory in required_dirs:
            if directory.exists():
                print(f"✅ {directory} directory found")
            else:
                print(f"❌ {directory} directory missing")
                validation_passed = False
        
        # Check framework files
        framework_files = [
            runner.base_path / "framework" / "__init__.py",
            runner.base_path / "framework" / "csv_utils.py", 
            runner.base_path / "framework" / "yaml_utils.py",
            runner.base_path / "framework" / "test_base.py"
        ]
        
        for file in framework_files:
            if file.exists():
                print(f"✅ {file.name} found")
            else:
                print(f"❌ {file.name} missing")
                validation_passed = False
        
        # Note about data per constraint
        print("ℹ️  Sample data is generated individually within each constraint directory")
        
        # Test YAML output (save to temporary location)
        try:
            test_result = {"validation": True, "timestamp": datetime.now().isoformat()}
            temp_dir = Path("temp")
            temp_dir.mkdir(exist_ok=True)
            test_file = temp_dir / "validation_test.yaml"
            save_yaml_results(test_result, str(test_file))
            print(f"✅ YAML output works - saved to {test_file}")
            # Clean up temp file
            test_file.unlink()
            temp_dir.rmdir()
        except Exception as e:
            print(f"❌ YAML output failed: {e}")
            validation_passed = False
        
        if validation_passed:
            print("\n🎉 Framework validation: PASSED")
        else:
            print("\n❌ Framework validation: FAILED")
            print("Run 'python run_tests.py --setup' to fix missing components")
            return
    
    if args.setup:
        runner.setup_framework()
        print("\n🔧 Framework setup complete. You can now run tests.")
        return
    
    # Run tests
    results = runner.run_all_tests(args.constraints)
    
    if results:
        success_rate = float(results['test_run_summary']['overall_success_rate'].rstrip('%'))
        if success_rate == 100:
            print("\n🏆 All tests passed! Framework is fully functional.")
        elif success_rate >= 80:
            print(f"\n✅ Framework is mostly functional ({success_rate}% success rate)")
        else:
            print(f"\n⚠️  Framework needs attention ({success_rate}% success rate)")
    
    print("\n📋 Next Steps:")
    print("   • Review individual constraint results in tests/constraints/")
    print("   • Run 'python run_tests.py --validate' if issues persist")

if __name__ == "__main__":
    main()
