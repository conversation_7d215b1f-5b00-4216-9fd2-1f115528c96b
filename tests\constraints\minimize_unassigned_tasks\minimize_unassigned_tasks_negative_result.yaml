test_metadata:
  constraint: minimize_unassigned_tasks
  scenario: negative
  test_framework_version: 1.0.0
  timestamp: '2025-06-01T15:20:56.486378'
test_result:
  constraint_satisfied: false
  details:
    assigned_tasks: 0
    assignment_rate: 0.0%
    penalty_calculation_verified: true
    resource_shortage_detected: true
    total_tasks: 5
    unassigned_task_analysis:
    - available_skills:
      - &id001
        - Cardiology
      - &id002
        - Dermatology
      priority: Medium
      reason: skill_mismatch
      required_skills:
      - General
      task_id: T001
    - available_skills:
      - *id001
      - *id002
      priority: High
      reason: skill_mismatch
      required_skills:
      - ICU
      task_id: T002
    - available_skills:
      - *id001
      - *id002
      priority: High
      reason: skill_mismatch
      required_skills:
      - Surgery
      task_id: T003
    - available_skills:
      - *id001
      - *id002
      priority: High
      reason: skill_mismatch
      required_skills:
      - Neurology
      task_id: T004
    - available_skills:
      - *id001
      - *id002
      priority: Medium
      reason: skill_mismatch
      required_skills:
      - Orthopedics
      task_id: T005
    unassigned_tasks: 5
  score_impact: -650
  test_status: PASS
  violation_count: 5
