"""
Healthcare-specific configuration for constraint provider.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional

@dataclass
class HealthcareConstraintConfig:
    """Healthcare-specific constraint configuration"""
    
    # Basic constraints
    minimum_rest_minutes: int = 30
    max_consecutive_hours: int = 12
    emergency_response_time_limit: int = 60  # minutes
    
    # Infection control
    infection_control_buffer_minutes: int = 15
    enable_infection_control: bool = True
    
    # Safety pairing
    safety_pairing_risk_threshold: int = 2
    enable_safety_pairing: bool = True
    
    # Continuity of care
    continuity_care_preference_weight: int = 15
    
    # Emergency handling
    emergency_penalty_multiplier: int = 2
    critical_response_time_minutes: int = 30
    
    # Compliance
    enable_hipaa_compliance: bool = True
    enable_overtime_tracking: bool = True    # Constraint weights
    constraint_weights: Optional[Dict[str, int]] = None
    
    def __post_init__(self):
        if self.constraint_weights is None:
            self.constraint_weights = {
                'emergency_response_time': 50,
                'infection_control_violation': 100,
                'safety_pairing_violation': 75,
                'hipaa_violation': 200,
                'overtime_penalty': 25,
                'visit_extension_penalty': 10
            }
    
    @classmethod
    def for_home_healthcare(cls) -> 'HealthcareConstraintConfig':
        """Configuration optimized for home healthcare"""
        return cls(
            minimum_rest_minutes=45,  # More travel time needed
            emergency_response_time_limit=45,
            safety_pairing_risk_threshold=3,  # More lenient for home visits
            continuity_care_preference_weight=25,  # Higher for home care
        )
    
    @classmethod
    def for_hospital_scheduling(cls) -> 'HealthcareConstraintConfig':
        """Configuration optimized for hospital scheduling"""
        return cls(
            minimum_rest_minutes=15,  # Less travel time
            max_consecutive_hours=16,  # Longer shifts possible
            emergency_response_time_limit=15,  # Faster response needed
            infection_control_buffer_minutes=30,  # Stricter protocols
        )
    
    @classmethod
    def for_emergency_response(cls) -> 'HealthcareConstraintConfig':
        """Configuration optimized for emergency response"""
        return cls(
            minimum_rest_minutes=20,
            emergency_response_time_limit=30,
            critical_response_time_minutes=15,
            emergency_penalty_multiplier=5,
            constraint_weights={
                'emergency_response_time': 200,
                'safety_pairing_violation': 150,
                'overtime_penalty': 5,  # Less important in emergencies
            }
        )
