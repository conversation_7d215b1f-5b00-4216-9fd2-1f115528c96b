import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'framework'))

from csv_utils import load_csv_data
from yaml_utils import save_yaml_results
from datetime import datetime, timedelta

class TestContinuityCare:
    def __init__(self):
        self.constraint_name = "continuity_care"
        self.constraint_dir = os.path.dirname(__file__)
    
    def load_data(self):
        """Load test data from CSV files"""
        self.caregivers = load_csv_data(os.path.join(self.constraint_dir, 'caregivers.csv'))
        self.patients = load_csv_data(os.path.join(self.constraint_dir, 'patients.csv'))
        self.visits = load_csv_data(os.path.join(self.constraint_dir, 'visits.csv'))
        
        print(f"Loaded {len(self.caregivers)} caregivers, {len(self.patients)} patients, {len(self.visits)} visits")
    
    def test_constraint(self):
        """Test continuity of care constraint"""
        print(f"\n=== Testing {self.constraint_name} constraint ===")
        
        results = {
            'constraint_name': self.constraint_name,
            'description': 'Ensure patients receive care from familiar caregivers to maintain continuity',
            'test_timestamp': datetime.now().isoformat(),
            'visits_evaluated': [],
            'summary': {
                'total_visits': len(self.visits),
                'high_continuity_matches': 0,
                'regular_caregiver_matches': 0,
                'new_caregiver_assignments': 0,
                'continuity_violations': 0
            }
        }
        
        for visit in self.visits:
            visit_result = self._evaluate_continuity(visit)
            results['visits_evaluated'].append(visit_result)
            
            # Update summary
            if visit_result['continuity_level'] == 'High':
                results['summary']['high_continuity_matches'] += 1
            elif visit_result['continuity_level'] == 'Regular':
                results['summary']['regular_caregiver_matches'] += 1
            elif visit_result['continuity_level'] == 'New':
                results['summary']['new_caregiver_assignments'] += 1
            else:
                results['summary']['continuity_violations'] += 1
        
        return results
    
    def _evaluate_continuity(self, visit):
        """Evaluate continuity of care for a single visit"""
        patient = next((p for p in self.patients if p['id'] == visit['patient_id']), None)
        if not patient:
            return {'error': f"Patient {visit['patient_id']} not found"}
        
        # Get patient's regular caregivers and last caregiver
        regular_caregivers = patient.get('regular_caregivers', '').split(',')
        regular_caregivers = [cg.strip() for cg in regular_caregivers if cg.strip()]
        last_caregiver = visit.get('last_caregiver', '')
        preferred_caregiver = patient.get('preferred_caregiver', '')
        
        # Find available caregivers who can handle this visit
        available_caregivers = []
        for caregiver in self.caregivers:
            caregiver_skills = caregiver.get('skills', '').split(',')
            caregiver_skills = [skill.strip() for skill in caregiver_skills]
            required_skills = visit.get('required_skills', '').split(',')
            required_skills = [skill.strip() for skill in required_skills]
            
            # Check if caregiver has required skills
            if all(skill in caregiver_skills for skill in required_skills):
                # Check if caregiver is available (simplified check)
                visit_time = datetime.strptime(visit['scheduled_time'], '%Y-%m-%d %H:%M')
                caregiver_start = datetime.strptime(f"2024-01-15 {caregiver['available_start']}", '%Y-%m-%d %H:%M')
                caregiver_end = datetime.strptime(f"2024-01-15 {caregiver['available_end']}", '%Y-%m-%d %H:%M')
                
                # Adjust for visit date
                caregiver_start = caregiver_start.replace(year=visit_time.year, month=visit_time.month, day=visit_time.day)
                caregiver_end = caregiver_end.replace(year=visit_time.year, month=visit_time.month, day=visit_time.day)
                
                if caregiver_start <= visit_time <= caregiver_end:
                    available_caregivers.append(caregiver)
        
        # Evaluate continuity options
        continuity_analysis = {
            'visit_id': visit['id'],
            'patient_name': patient['name'],
            'required_skills': visit['required_skills'],
            'last_caregiver': last_caregiver,
            'preferred_caregiver': preferred_caregiver,
            'regular_caregivers': regular_caregivers,
            'available_caregivers': [cg['name'] for cg in available_caregivers],
            'continuity_options': [],
            'recommended_assignment': None,
            'continuity_level': 'None',
            'rationale': ''
        }
        
        # Analyze continuity options
        for caregiver in available_caregivers:
            caregiver_name = caregiver['name']
            continuity_score = 0
            continuity_type = 'New'
            reasons = []
            
            # Check continuity factors
            if caregiver_name == last_caregiver:
                continuity_score += 50
                continuity_type = 'Same as Last'
                reasons.append('Same caregiver as last visit')
            
            if caregiver_name == preferred_caregiver:
                continuity_score += 40
                continuity_type = 'Preferred'
                reasons.append('Patient\'s preferred caregiver')
            
            if caregiver_name in regular_caregivers:
                continuity_score += 30
                if continuity_type == 'New':
                    continuity_type = 'Regular'
                reasons.append('Regular caregiver for this patient')
            
            # Check if caregiver has patient in their regular list
            caregiver_regular_patients = caregiver.get('regular_patients', '').split(',')
            caregiver_regular_patients = [p.strip() for p in caregiver_regular_patients if p.strip()]
            if visit['patient_id'] in caregiver_regular_patients:
                continuity_score += 25
                reasons.append('Patient is in caregiver\'s regular caseload')
            
            continuity_analysis['continuity_options'].append({
                'caregiver_name': caregiver_name,
                'continuity_score': continuity_score,
                'continuity_type': continuity_type,
                'reasons': reasons,
                'hourly_rate': caregiver['hourly_rate']
            })
        
        # Sort by continuity score and select best option
        if continuity_analysis['continuity_options']:
            best_option = max(continuity_analysis['continuity_options'], key=lambda x: x['continuity_score'])
            continuity_analysis['recommended_assignment'] = best_option
            
            # Determine overall continuity level
            if best_option['continuity_score'] >= 50:
                continuity_analysis['continuity_level'] = 'High'
                continuity_analysis['rationale'] = 'Excellent continuity - same or preferred caregiver available'
            elif best_option['continuity_score'] >= 30:
                continuity_analysis['continuity_level'] = 'Regular'
                continuity_analysis['rationale'] = 'Good continuity - regular caregiver available'
            elif best_option['continuity_score'] >= 10:
                continuity_analysis['continuity_level'] = 'Low'
                continuity_analysis['rationale'] = 'Some continuity maintained'
            else:
                continuity_analysis['continuity_level'] = 'New'
                continuity_analysis['rationale'] = 'New caregiver assignment - no previous relationship'
        else:
            continuity_analysis['continuity_level'] = 'Violation'
            continuity_analysis['rationale'] = 'No available caregivers with required skills'
        
        return continuity_analysis
    
    def save_results(self, results):
        """Save test results to YAML file"""
        output_file = os.path.join(self.constraint_dir, f'{self.constraint_name}_results.yaml')
        save_yaml_results(results, output_file)
        print(f"Results saved to: {output_file}")

def run_test():
    """Run the continuity care constraint test"""
    test = TestContinuityCare()
    test.load_data()
    results = test.test_constraint()
    test.save_results(results)
    
    # Print summary
    print(f"\n=== Continuity Care Test Summary ===")
    print(f"Total visits evaluated: {results['summary']['total_visits']}")
    print(f"High continuity matches: {results['summary']['high_continuity_matches']}")
    print(f"Regular caregiver matches: {results['summary']['regular_caregiver_matches']}")
    print(f"New caregiver assignments: {results['summary']['new_caregiver_assignments']}")
    print(f"Continuity violations: {results['summary']['continuity_violations']}")

if __name__ == "__main__":
    run_test()
