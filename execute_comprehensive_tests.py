#!/usr/bin/env python3
"""Comprehensive Test Framework Execution Script"""

import sys
import os
from pathlib import Path
from datetime import datetime
import importlib.util

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tests.framework.yaml_output import YAMLTestOutput


class TestFrameworkExecutor:
    """Execute all constraint tests and generate comprehensive reports"""
    
    def __init__(self):
        self.test_results = {}
        self.execution_summary = {
            'execution_timestamp': datetime.now().isoformat(),
            'framework_version': '1.0.0',
            'total_constraints_tested': 0,
            'constraints_passed': 0,
            'constraints_failed': 0,
            'constraints_error': 0,
            'total_scenarios': 0,
            'scenarios_passed': 0,
            'scenarios_failed': 0
        }
        self.output_manager = YAMLTestOutput("tests/output")
    
    def discover_and_run_tests(self):
        """Discover all constraint tests and execute them"""
        print("🔍 Discovering Constraint Tests...")
        
        constraints_dir = project_root / "tests" / "constraints"
        
        if not constraints_dir.exists():
            print(f"❌ Constraints directory not found: {constraints_dir}")
            return
        
        # Find all constraint test directories
        constraint_dirs = [d for d in constraints_dir.iterdir() if d.is_dir()]
        
        for constraint_dir in constraint_dirs:
            constraint_name = constraint_dir.name
            test_file = constraint_dir / f"test_{constraint_name}.py"
            
            if test_file.exists():
                print(f"\n📋 Testing Constraint: {constraint_name}")
                print("-" * 50)
                
                try:
                    # Import and execute the test
                    result = self._execute_constraint_test(test_file, constraint_name)
                    self.test_results[constraint_name] = result
                    
                    if result['overall_status'] == 'PASS':
                        self.execution_summary['constraints_passed'] += 1
                    elif result['overall_status'] == 'FAIL':
                        self.execution_summary['constraints_failed'] += 1
                    
                    self.execution_summary['total_scenarios'] += result['scenarios_run']
                    self.execution_summary['scenarios_passed'] += result['scenarios_passed']
                    self.execution_summary['scenarios_failed'] += result['scenarios_failed']
                    
                except Exception as e:
                    print(f"❌ Error executing {constraint_name}: {e}")
                    self.test_results[constraint_name] = {
                        'overall_status': 'ERROR',
                        'error_message': str(e)
                    }
                    self.execution_summary['constraints_error'] += 1
            else:
                print(f"⚠️  Test file not found for {constraint_name}")
        
        self.execution_summary['total_constraints_tested'] = len(self.test_results)
        
    def _execute_constraint_test(self, test_file_path: Path, constraint_name: str):
        """Execute a single constraint test file"""
        
        # Dynamic import of the test module
        spec = importlib.util.spec_from_file_location(f"test_{constraint_name}", test_file_path)
        test_module = importlib.util.module_from_spec(spec)
        
        try:
            spec.loader.exec_module(test_module)
        except Exception as e:
            return {
                'overall_status': 'ERROR',
                'error_message': f"Failed to import test module: {e}",
                'scenarios_run': 0,
                'scenarios_passed': 0,
                'scenarios_failed': 0
            }
        
        # Find the test class (assumes naming convention)
        test_class_name = f"Test{self._camel_case(constraint_name)}Constraint"
        
        if not hasattr(test_module, test_class_name):
            return {
                'overall_status': 'ERROR',
                'error_message': f"Test class {test_class_name} not found",
                'scenarios_run': 0,
                'scenarios_passed': 0,
                'scenarios_failed': 0
            }
        
        # Instantiate and run tests
        test_class = getattr(test_module, test_class_name)
        test_instance = test_class()
        
        scenario_results = {}
        scenarios_passed = 0
        scenarios_failed = 0
        
        # Run each scenario
        for scenario in ['positive', 'negative', 'edge']:
            method_name = f"test_{scenario}_scenario"
            
            if hasattr(test_instance, method_name):
                print(f"  🧪 Running {scenario} scenario...")
                
                try:
                    method = getattr(test_instance, method_name)
                    result_file = method()
                    
                    # Load and analyze the result
                    scenario_results[scenario] = {
                        'status': 'PASS',
                        'result_file': result_file
                    }
                    scenarios_passed += 1
                    print(f"    ✅ {scenario.capitalize()} scenario: PASS")
                    
                except Exception as e:
                    scenario_results[scenario] = {
                        'status': 'FAIL',
                        'error': str(e)
                    }
                    scenarios_failed += 1
                    print(f"    ❌ {scenario.capitalize()} scenario: FAIL - {e}")
        
        # Determine overall status
        overall_status = 'PASS' if scenarios_failed == 0 else 'FAIL'
        
        return {
            'overall_status': overall_status,
            'scenarios_run': scenarios_passed + scenarios_failed,
            'scenarios_passed': scenarios_passed,
            'scenarios_failed': scenarios_failed,
            'scenario_details': scenario_results
        }
    
    def _camel_case(self, snake_str: str) -> str:
        """Convert snake_case to CamelCase"""
        components = snake_str.split('_')
        return ''.join(word.capitalize() for word in components)
    
    def generate_comprehensive_report(self):
        """Generate a comprehensive test execution report"""
        print("\n📊 Generating Comprehensive Test Report...")
        
        # Calculate success rate
        total_constraints = self.execution_summary['total_constraints_tested']
        passed_constraints = self.execution_summary['constraints_passed']
        
        if total_constraints > 0:
            success_rate = (passed_constraints / total_constraints) * 100
        else:
            success_rate = 0
        
        self.execution_summary['overall_success_rate'] = f"{success_rate:.1f}%"
        
        # Prepare detailed report
        comprehensive_report = {
            'test_execution_summary': self.execution_summary,
            'constraint_test_results': {}
        }
        
        # Add detailed results for each constraint
        for constraint_name, result in self.test_results.items():
            comprehensive_report['constraint_test_results'][constraint_name] = result
        
        # Save the comprehensive report
        report_file = self.output_manager.save_test_result(
            "framework_execution", 
            "comprehensive", 
            comprehensive_report
        )
        
        print(f"📄 Comprehensive report saved to: {report_file}")
        
        # Print summary to console
        self._print_execution_summary()
        
        return report_file
    
    def _print_execution_summary(self):
        """Print execution summary to console"""
        summary = self.execution_summary
        
        print(f"\n🎯 Test Execution Summary")
        print("=" * 50)
        print(f"📅 Execution Time: {summary['execution_timestamp']}")
        print(f"🔧 Framework Version: {summary['framework_version']}")
        print(f"📋 Total Constraints Tested: {summary['total_constraints_tested']}")
        print(f"✅ Constraints Passed: {summary['constraints_passed']}")
        print(f"❌ Constraints Failed: {summary['constraints_failed']}")
        print(f"🚨 Constraints Error: {summary['constraints_error']}")
        print(f"🧪 Total Scenarios: {summary['total_scenarios']}")
        print(f"✅ Scenarios Passed: {summary['scenarios_passed']}")
        print(f"❌ Scenarios Failed: {summary['scenarios_failed']}")
        print(f"📊 Overall Success Rate: {summary['overall_success_rate']}")
        
        # Print individual constraint results
        print(f"\n📋 Individual Constraint Results:")
        print("-" * 30)
        
        for constraint_name, result in self.test_results.items():
            status_emoji = "✅" if result['overall_status'] == 'PASS' else "❌" if result['overall_status'] == 'FAIL' else "🚨"
            print(f"{status_emoji} {constraint_name}: {result['overall_status']}")
            
            if 'scenario_details' in result:
                for scenario, details in result['scenario_details'].items():
                    scenario_emoji = "✅" if details['status'] == 'PASS' else "❌"
                    print(f"    {scenario_emoji} {scenario}: {details['status']}")


def main():
    """Main execution function"""
    print("🏥 Healthcare Scheduling Test Framework - Comprehensive Execution")
    print("=" * 70)
    
    executor = TestFrameworkExecutor()
    
    # Discover and run all tests
    executor.discover_and_run_tests()
    
    # Generate comprehensive report
    report_file = executor.generate_comprehensive_report()
    
    print(f"\n🎉 Test Framework Execution Complete!")
    print(f"📄 Full report available at: {report_file}")


if __name__ == "__main__":
    main()
