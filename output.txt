Traceback (most recent call last):
  File "d:\Work\clinician-scheduler-python\quick_test_generator.py", line 521, in <module>
    main()
  File "d:\Work\clinician-scheduler-python\quick_test_generator.py", line 16, in main
    print("\U0001f680 Generating Healthcare Scheduling Test Framework...")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>
