"""Base test classes for constraint testing"""
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from .csv_parser import CSVTestDataParser
from .yaml_output import YAMLTestOutput


class ConstraintTestBase:
    """Base class for all constraint tests"""

    def __init__(self, name: str):
        self.name = name
        self.results = {}
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.test_dir = Path(__file__).parent.parent / "constraints" / name
        self.parser = CSVTestDataParser(str(self.test_dir))
        self.output = YAMLTestOutput(str(self.test_dir))

    def setup(self):
        """Setup test environment"""
        self.start_time = datetime.now()
        print(f"🧪 Starting {self.name} constraint test...")

    def teardown(self):
        """Cleanup after test"""
        self.end_time = datetime.now()
        if self.start_time:
            duration = (self.end_time - self.start_time).total_seconds()
            print(f"✅ {self.name} completed in {duration:.2f}s")

    def validate_data(self, data: List[Dict[str, Any]]) -> bool:
        """Validate input data"""
        if not data:
            print(f"❌ No data provided for {self.name}")
            return False
        return True

    def log_result(self, constraint: str, passed: bool, details: str = ""):
        """Log a constraint test result"""
        self.results[constraint] = {
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }

    def _save_result(self, scenario: str, result: Dict[str, Any]) -> bool:
        """Save test result to YAML file"""
        try:
            self.output.save_test_result(self.name, scenario, result)
            return result.get('test_status') == 'PASS'
        except Exception as e:
            print(f"Error saving result: {e}")
            return False


class HardConstraintTest(ConstraintTestBase):
    """Base class for hard constraint tests"""

    def __init__(self, name: str):
        super().__init__(name)
        self.constraint_type = "hard"

    def test_positive_scenario(self):
        """Test scenario where constraint should be satisfied"""
        raise NotImplementedError("Subclasses must implement test_positive_scenario")

    def test_negative_scenario(self):
        """Test scenario where constraint should be violated"""
        raise NotImplementedError("Subclasses must implement test_negative_scenario")

    def test_edge_scenario(self):
        """Test edge cases for the constraint"""
        raise NotImplementedError("Subclasses must implement test_edge_scenario")


class SoftConstraintTest(ConstraintTestBase):
    """Base class for soft constraint tests"""

    def __init__(self, name: str):
        super().__init__(name)
        self.constraint_type = "soft"

    def test_positive_scenario(self):
        """Test scenario where constraint optimization works well"""
        raise NotImplementedError("Subclasses must implement test_positive_scenario")

    def test_negative_scenario(self):
        """Test scenario where constraint optimization is challenged"""
        raise NotImplementedError("Subclasses must implement test_negative_scenario")

    def test_edge_scenario(self):
        """Test edge cases for the constraint optimization"""
        raise NotImplementedError("Subclasses must implement test_edge_scenario")


class HealthcareConstraintTest(ConstraintTestBase):
    """Base class for healthcare-specific constraint tests"""

    def __init__(self, name: str):
        super().__init__(name)
        self.constraint_type = "healthcare"

    def test_positive_scenario(self):
        """Test scenario where healthcare constraint is satisfied"""
        raise NotImplementedError("Subclasses must implement test_positive_scenario")

    def test_negative_scenario(self):
        """Test scenario where healthcare constraint is violated"""
        raise NotImplementedError("Subclasses must implement test_negative_scenario")

    def test_edge_scenario(self):
        """Test edge cases for the healthcare constraint"""
        raise NotImplementedError("Subclasses must implement test_edge_scenario")