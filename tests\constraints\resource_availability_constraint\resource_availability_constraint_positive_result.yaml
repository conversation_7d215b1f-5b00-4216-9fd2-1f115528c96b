test_metadata:
  constraint: resource_availability_constraint
  scenario: positive
  test_framework_version: 1.0.0
  timestamp: '2025-06-01T15:20:56.500177'
test_result:
  constraint_satisfied: true
  details:
    availability_violations: []
    successful_assignments: 4
    total_tasks: 4
    valid_assignments:
    - assigned_clinician: C001
      availability_confirmed: true
      task_id: T001
    - assigned_clinician: C001
      availability_confirmed: true
      task_id: T002
    - assigned_clinician: C003
      availability_confirmed: true
      task_id: T003
    - assigned_clinician: C001
      availability_confirmed: true
      task_id: T004
  score_impact: 0
  test_status: PASS
  violation_count: 0
