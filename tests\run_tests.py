#!/usr/bin/env python3
"""
Comprehensive Test Runner for Home Care Scheduling Constraints

This script runs all constraint tests in the streamlined test framework.
It provides detailed results and summaries for each constraint type.
"""

import os
import sys
import importlib.util
from datetime import datetime

# Add framework to path
framework_path = os.path.join(os.path.dirname(__file__), 'framework')
sys.path.insert(0, framework_path)

class TestRunner:
    def __init__(self):
        self.test_dir = os.path.dirname(__file__)
        self.constraints_dir = os.path.join(self.test_dir, 'constraints')
        self.reports_dir = os.path.join(self.test_dir, 'reports')
        
        # Ensure reports directory exists
        os.makedirs(self.reports_dir, exist_ok=True)
        
        self.test_results = []
        
    def discover_constraints(self):
        """Discover all constraint test directories"""
        constraints = []
        if os.path.exists(self.constraints_dir):
            for item in os.listdir(self.constraints_dir):
                constraint_path = os.path.join(self.constraints_dir, item)
                if os.path.isdir(constraint_path):
                    test_file = os.path.join(constraint_path, f'test_{item}.py')
                    if os.path.exists(test_file):
                        constraints.append(item)
        return sorted(constraints)
    
    def run_constraint_test(self, constraint_name):
        """Run a specific constraint test"""
        print(f"\n{'='*60}")
        print(f"Running {constraint_name} constraint test...")
        print(f"{'='*60}")
        
        try:
            # Import the test module
            test_file = os.path.join(self.constraints_dir, constraint_name, f'test_{constraint_name}.py')
            spec = importlib.util.spec_from_file_location(f'test_{constraint_name}', test_file)
            test_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(test_module)
            
            # Run the test
            if hasattr(test_module, 'run_test'):
                test_module.run_test()
                return {'constraint': constraint_name, 'status': 'Success', 'error': None}
            else:
                print(f"Warning: No run_test function found in {constraint_name}")
                return {'constraint': constraint_name, 'status': 'Warning', 'error': 'No run_test function'}
                
        except Exception as e:
            print(f"Error running {constraint_name} test: {str(e)}")
            return {'constraint': constraint_name, 'status': 'Error', 'error': str(e)}
    
    def run_all_tests(self):
        """Run all constraint tests"""
        print("Home Care Scheduling Constraint Test Framework")
        print("=" * 60)
        
        constraints = self.discover_constraints()
        
        if not constraints:
            print("No constraint tests found!")
            return
        
        print(f"Found {len(constraints)} constraint tests:")
        for constraint in constraints:
            print(f"  - {constraint}")
        
        # Run each test
        for constraint in constraints:
            result = self.run_constraint_test(constraint)
            self.test_results.append(result)
        
        # Generate summary report
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """Generate a summary report of all test results"""
        print(f"\n{'='*60}")
        print("TEST EXECUTION SUMMARY")
        print(f"{'='*60}")
        
        success_count = sum(1 for r in self.test_results if r['status'] == 'Success')
        warning_count = sum(1 for r in self.test_results if r['status'] == 'Warning')
        error_count = sum(1 for r in self.test_results if r['status'] == 'Error')
        
        print(f"Total constraints tested: {len(self.test_results)}")
        print(f"Successful: {success_count}")
        print(f"Warnings: {warning_count}")
        print(f"Errors: {error_count}")
        
        print(f"\nDetailed Results:")
        for result in self.test_results:
            status_symbol = "✓" if result['status'] == 'Success' else "⚠" if result['status'] == 'Warning' else "✗"
            print(f"  {status_symbol} {result['constraint']}: {result['status']}")
            if result['error']:
                print(f"    Error: {result['error']}")
        
        # Generate summary file
        summary_file = os.path.join(self.reports_dir, f"test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        with open(summary_file, 'w') as f:
            f.write("Home Care Scheduling Constraint Test Summary\n")
            f.write("=" * 50 + "\n")
            f.write(f"Test Run: {datetime.now().isoformat()}\n\n")
            f.write(f"Total constraints tested: {len(self.test_results)}\n")
            f.write(f"Successful: {success_count}\n")
            f.write(f"Warnings: {warning_count}\n")
            f.write(f"Errors: {error_count}\n\n")
            
            f.write("Detailed Results:\n")
            for result in self.test_results:
                f.write(f"- {result['constraint']}: {result['status']}\n")
                if result['error']:
                    f.write(f"  Error: {result['error']}\n")
        
        print(f"\nSummary report saved to: {summary_file}")
    
    def run_specific_constraint(self, constraint_name):
        """Run a specific constraint test by name"""
        constraints = self.discover_constraints()
        
        if constraint_name not in constraints:
            print(f"Error: Constraint '{constraint_name}' not found.")
            print(f"Available constraints: {', '.join(constraints)}")
            return
        
        result = self.run_constraint_test(constraint_name)
        self.test_results.append(result)
        
        print(f"\nTest Result for {constraint_name}: {result['status']}")
        if result['error']:
            print(f"Error: {result['error']}")

def main():
    """Main entry point for the test runner"""
    runner = TestRunner()
    
    if len(sys.argv) > 1:
        # Run specific constraint
        constraint_name = sys.argv[1]
        runner.run_specific_constraint(constraint_name)
    else:
        # Run all tests
        runner.run_all_tests()

if __name__ == "__main__":
    main()
